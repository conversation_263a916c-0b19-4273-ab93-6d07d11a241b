import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { userService, reviewService } from '@/lib/database';
import { User, Mail, Phone, Calendar, LogOut, Settings, Star, Trophy, MapPin, Edit3, Shield, AlertTriangle, Users, MessageCircle } from 'lucide-react-native';

export default function PlayerProfile() {
  const { user, signOut } = useAuth();
  const { bookings, reviews, strikes, forumPosts, forumOffers, updateUser } = useData();
  const router = useRouter();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  const [editFormData, setEditFormData] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
    address: user?.address || '',
    city: user?.city || '',
  });
  const [playerReviews, setPlayerReviews] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // Calculate real stats from database
  const playerBookings = bookings.filter(booking => booking.player_id === user?.id);
  const completedBookings = playerBookings.filter(booking => booking.status === 'completed');
  const uniqueVenues = new Set(playerBookings.map(booking => booking.venue_id)).size;
  const playerStrikes = strikes.filter(strike => strike.user_id === user?.id && strike.status === 'active');

  // Forum statistics
  const playerForumPosts = forumPosts.filter(post => post.player_id === user?.id);
  const playerOffers = forumOffers.filter(offer => offer.offering_player_id === user?.id);
  const receivedOffers = forumOffers.filter(offer =>
    forumPosts.some(post => post.id === offer.forum_post_id && post.player_id === user?.id)
  );

  // Calculate average rating from reviews
  const playerRatingReviews = reviews.filter(review =>
    review.reviewee_id === user?.id && review.review_type === 'venue_to_player'
  );
  const averageRating = playerRatingReviews.length > 0
    ? (playerRatingReviews.reduce((sum, review) => sum + review.rating, 0) / playerRatingReviews.length).toFixed(1)
    : '0.0';

  const stats = [
    { label: 'Total Bookings', value: playerBookings.length.toString(), icon: Calendar },
    { label: 'Forum Posts', value: playerForumPosts.length.toString(), icon: Users },
    { label: 'Offers Made', value: playerOffers.length.toString(), icon: MessageCircle },
    { label: 'Average Rating', value: averageRating, icon: Star },
  ];

  useEffect(() => {
    if (user?.id) {
      fetchPlayerReviews();
    }
  }, [user?.id]);

  const fetchPlayerReviews = async () => {
    try {
      const reviews = await reviewService.getReviewsForPlayer(user!.id);
      setPlayerReviews(reviews || []);
    } catch (error) {
      console.error('Error fetching player reviews:', error);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await signOut();
            router.replace('/auth');
          }
        },
      ]
    );
  };

  const handleUpdateProfile = async () => {
    try {
      setLoading(true);
      await updateUser(user!.id, editFormData);

      setEditModalVisible(false);
      Alert.alert('Success', 'Profile updated successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.profileContainer}>
            <View style={styles.avatar}>
              {user?.profile_image_url ? (
                <Image source={{ uri: user.profile_image_url }} style={styles.avatarImage} />
              ) : (
                <User size={32} color="#22C55E" />
              )}
            </View>
            <Text style={styles.name}>{user?.name}</Text>
            <Text style={styles.role}>Player</Text>
            <View style={styles.statusContainer}>
              <View style={[styles.statusBadge, {
                backgroundColor: user?.status === 'approved' ? '#22C55E20' : '#F59E0B20'
              }]}>
                <Text style={[styles.statusText, {
                  color: user?.status === 'approved' ? '#22C55E' : '#F59E0B'
                }]}>
                  {user?.status?.charAt(0).toUpperCase() + user?.status?.slice(1)}
                </Text>
              </View>
              {playerStrikes.length > 0 && (
                <View style={styles.strikesBadge}>
                  <AlertTriangle size={12} color="#EF4444" />
                  <Text style={styles.strikesText}>{playerStrikes.length} Strike{playerStrikes.length > 1 ? 's' : ''}</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        <View style={styles.statsContainer}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statCard}>
              <stat.icon size={20} color={stat.color || "#22C55E"} />
              <Text style={[styles.statValue, stat.color && { color: stat.color }]}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Account Information</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setEditModalVisible(true)}
            >
              <Edit3 size={16} color="#3B82F6" />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Mail size={20} color="#6B7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{user?.email}</Text>
              </View>
            </View>

            <View style={styles.infoRow}>
              <Phone size={20} color="#6B7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{user?.phone || 'Not provided'}</Text>
              </View>
            </View>

            <View style={styles.infoRow}>
              <MapPin size={20} color="#6B7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>{user?.address || 'Not provided'}</Text>
              </View>
            </View>

            <View style={styles.infoRow}>
              <MapPin size={20} color="#6B7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>City</Text>
                <Text style={styles.infoValue}>{user?.city || 'Not provided'}</Text>
              </View>
            </View>

            {user?.cnic && (
              <View style={styles.infoRow}>
                <Shield size={20} color="#6B7280" />
                <View style={styles.infoText}>
                  <Text style={styles.infoLabel}>CNIC</Text>
                  <Text style={styles.infoValue}>{user.cnic}</Text>
                </View>
              </View>
            )}

            <View style={styles.infoRow}>
              <Calendar size={20} color="#6B7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Member Since</Text>
                <Text style={styles.infoValue}>
                  {new Date(user?.created_at || '').toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Forum Activity Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Forum Activity</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => router.push('/(player-tabs)/forum')}
            >
              <Users size={16} color="#3B82F6" />
              <Text style={styles.editButtonText}>View All</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.forumActivityContainer}>
            <View style={styles.forumStatCard}>
              <View style={styles.forumStatHeader}>
                <Users size={20} color="#22C55E" />
                <Text style={styles.forumStatTitle}>My Posts</Text>
              </View>
              <Text style={styles.forumStatValue}>{playerForumPosts.length}</Text>
              <Text style={styles.forumStatLabel}>Active forum posts</Text>
            </View>

            <View style={styles.forumStatCard}>
              <View style={styles.forumStatHeader}>
                <MessageCircle size={20} color="#3B82F6" />
                <Text style={styles.forumStatTitle}>Offers Made</Text>
              </View>
              <Text style={styles.forumStatValue}>{playerOffers.length}</Text>
              <Text style={styles.forumStatLabel}>Total offers sent</Text>
            </View>

            <View style={styles.forumStatCard}>
              <View style={styles.forumStatHeader}>
                <MessageCircle size={20} color="#F59E0B" />
                <Text style={styles.forumStatTitle}>Offers Received</Text>
              </View>
              <Text style={styles.forumStatValue}>{receivedOffers.length}</Text>
              <Text style={styles.forumStatLabel}>Offers on my posts</Text>
            </View>
          </View>

          {/* Recent Forum Posts */}
          {playerForumPosts.length > 0 && (
            <View style={styles.recentPostsContainer}>
              <Text style={styles.subSectionTitle}>Recent Posts</Text>
              {playerForumPosts.slice(0, 2).map((post) => (
                <TouchableOpacity
                  key={post.id}
                  style={styles.forumPostCard}
                  onPress={() => router.push(`/forum/post/${post.id}`)}
                >
                  <Text style={styles.forumPostTitle} numberOfLines={1}>
                    {post.title}
                  </Text>
                  <Text style={styles.forumPostDetails}>
                    Looking for {post.looking_for_players} player{post.looking_for_players > 1 ? 's' : ''}
                  </Text>
                  <View style={styles.forumPostFooter}>
                    <Text style={styles.forumPostStatus}>
                      Status: {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
                    </Text>
                    <Text style={styles.forumPostDate}>
                      {new Date(post.created_at).toLocaleDateString()}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Recent Reviews Section */}
        {playerReviews.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Reviews</Text>
            <View style={styles.reviewsContainer}>
              {playerReviews.slice(0, 3).map((review, index) => (
                <View key={index} style={styles.reviewCard}>
                  <View style={styles.reviewHeader}>
                    <Text style={styles.reviewVenue}>{review.venue?.name}</Text>
                    <View style={styles.reviewRating}>
                      <Star size={14} color="#F59E0B" fill="#F59E0B" />
                      <Text style={styles.reviewRatingText}>{review.rating}</Text>
                    </View>
                  </View>
                  {review.comment && (
                    <Text style={styles.reviewComment} numberOfLines={2}>{review.comment}</Text>
                  )}
                  <Text style={styles.reviewDate}>
                    {new Date(review.created_at).toLocaleDateString()}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>

          <View style={styles.menuCard}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => setSettingsModalVisible(true)}
            >
              <Settings size={20} color="#6B7280" />
              <Text style={styles.menuText}>Account Settings</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem} onPress={handleSignOut}>
              <LogOut size={20} color="#EF4444" />
              <Text style={[styles.menuText, { color: '#EF4444' }]}>Sign Out</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Edit Profile Modal */}
      <Modal
        visible={editModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setEditModalVisible(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Profile</Text>
            <TouchableOpacity onPress={handleUpdateProfile} disabled={loading}>
              <Text style={[styles.modalSaveText, loading && { opacity: 0.5 }]}>
                {loading ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Full Name</Text>
              <TextInput
                style={styles.input}
                value={editFormData.name}
                onChangeText={(text) => setEditFormData(prev => ({ ...prev, name: text }))}
                placeholder="Enter your full name"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <TextInput
                style={styles.input}
                value={editFormData.phone}
                onChangeText={(text) => setEditFormData(prev => ({ ...prev, phone: text }))}
                placeholder="Enter your phone number"
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Address</Text>
              <TextInput
                style={styles.input}
                value={editFormData.address}
                onChangeText={(text) => setEditFormData(prev => ({ ...prev, address: text }))}
                placeholder="Enter your address"
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>City</Text>
              <TextInput
                style={styles.input}
                value={editFormData.city}
                onChangeText={(text) => setEditFormData(prev => ({ ...prev, city: text }))}
                placeholder="Enter your city"
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Account Settings Modal */}
      <Modal
        visible={settingsModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setSettingsModalVisible(false)}>
              <Text style={styles.modalCancelText}>Close</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Account Settings</Text>
            <View style={{ width: 50 }} />
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.settingsSection}>
              <Text style={styles.settingsSectionTitle}>Privacy & Security</Text>

              <View style={styles.settingsCard}>
                <TouchableOpacity style={styles.settingsItem}>
                  <Shield size={20} color="#6B7280" />
                  <View style={styles.settingsText}>
                    <Text style={styles.settingsLabel}>Change Password</Text>
                    <Text style={styles.settingsDescription}>Update your account password</Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingsItem}>
                  <User size={20} color="#6B7280" />
                  <View style={styles.settingsText}>
                    <Text style={styles.settingsLabel}>Privacy Settings</Text>
                    <Text style={styles.settingsDescription}>Control who can see your profile</Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.settingsSection}>
              <Text style={styles.settingsSectionTitle}>Notifications</Text>

              <View style={styles.settingsCard}>
                <TouchableOpacity style={styles.settingsItem}>
                  <Text style={styles.settingsLabel}>Booking Confirmations</Text>
                  <Text style={styles.settingsDescription}>Get notified when bookings are confirmed</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingsItem}>
                  <Text style={styles.settingsLabel}>Venue Updates</Text>
                  <Text style={styles.settingsDescription}>Receive updates from venues you've booked</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.settingsSection}>
              <Text style={styles.settingsSectionTitle}>Support</Text>

              <View style={styles.settingsCard}>
                <TouchableOpacity style={styles.settingsItem}>
                  <Text style={styles.settingsLabel}>Help Center</Text>
                  <Text style={styles.settingsDescription}>Get help and support</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingsItem}>
                  <Text style={styles.settingsLabel}>Contact Us</Text>
                  <Text style={styles.settingsDescription}>Reach out to our support team</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingsItem}>
                  <Text style={styles.settingsLabel}>Terms & Conditions</Text>
                  <Text style={styles.settingsDescription}>Read our terms and conditions</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.settingsSection}>
              <Text style={styles.settingsSectionTitle}>Account</Text>

              <View style={styles.settingsCard}>
                <TouchableOpacity style={styles.settingsItem}>
                  <Text style={[styles.settingsLabel, { color: '#EF4444' }]}>Delete Account</Text>
                  <Text style={styles.settingsDescription}>Permanently delete your account</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 32,
  },
  profileContainer: {
    alignItems: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    overflow: 'hidden',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  name: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  role: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#22C55E',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  strikesBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  strikesText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginTop: -16,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  editButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  infoText: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  menuCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    flex: 1,
  },
  // Reviews styles
  reviewsContainer: {
    gap: 12,
  },
  reviewCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewVenue: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
  },
  reviewRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  reviewRatingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  reviewComment: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 8,
    lineHeight: 20,
  },
  reviewDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  modalCancelText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalSaveText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    textAlignVertical: 'top',
  },
  // Settings modal styles
  settingsSection: {
    marginBottom: 24,
  },
  settingsSectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  settingsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingsText: {
    marginLeft: 12,
    flex: 1,
  },
  settingsLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 2,
  },
  settingsDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
});