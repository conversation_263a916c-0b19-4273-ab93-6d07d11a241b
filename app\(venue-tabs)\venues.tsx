import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { Star, MapPin, DollarSign, CreditCard as Edit, Eye, Clock } from 'lucide-react-native';

export default function VenueOwnerVenues() {
  const { user } = useAuth();
  const { venues } = useData();

  const ownerVenues = venues.filter(venue => venue.owner_id === user?.id);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#22C55E';
      case 'rejected':
        return '#EF4444';
      default:
        return '#F59E0B';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <Eye size={14} color="#22C55E" />;
      case 'rejected':
        return <Eye size={14} color="#EF4444" />;
      default:
        return <Clock size={14} color="#F59E0B" />;
    }
  };

  const renderVenueCard = (venue: any) => (
    <View key={venue.id} style={styles.venueCard}>
      <Image
        source={{ uri: venue.images?.[0] || 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg' }}
        style={styles.venueImage}
        resizeMode="cover"
      />
      <View style={styles.venueInfo}>
        <View style={styles.venueHeader}>
          <Text style={styles.venueName} numberOfLines={1}>
            {venue.name}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(venue.status)}20` }]}>
            {getStatusIcon(venue.status)}
            <Text style={[styles.statusText, { color: getStatusColor(venue.status) }]}>
              {venue.status.charAt(0).toUpperCase() + venue.status.slice(1)}
            </Text>
          </View>
        </View>
        
        <View style={styles.venueLocation}>
          <MapPin size={14} color="#6B7280" />
          <Text style={styles.locationText} numberOfLines={1}>
            {venue.address}, {venue.city}
          </Text>
        </View>
        
        <View style={styles.venueStats}>
          <View style={styles.statItem}>
            <DollarSign size={16} color="#22C55E" />
            <Text style={styles.statText}>${venue.price_per_hour}/hr</Text>
          </View>
          <View style={styles.statItem}>
            <Star size={16} color="#F59E0B" fill="#F59E0B" />
            <Text style={styles.statText}>{venue.rating}</Text>
          </View>
        </View>
        
        <View style={styles.venueActions}>
          <TouchableOpacity style={styles.editButton}>
            <Edit size={16} color="#3B82F6" />
            <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>
          <Text style={styles.bookingsCount}>{venue.total_bookings} bookings</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>My Venues</Text>
        <Text style={styles.subtitle}>
          {ownerVenues.length} venue{ownerVenues.length !== 1 ? 's' : ''}
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {ownerVenues.length > 0 ? (
          <View style={styles.venueList}>
            {ownerVenues.map(renderVenueCard)}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <MapPin size={48} color="#D1D5DB" />
            <Text style={styles.emptyStateTitle}>No venues yet</Text>
            <Text style={styles.emptyStateText}>
              Start by adding your first venue to begin receiving bookings
            </Text>
            <TouchableOpacity style={styles.addVenueButton}>
              <Text style={styles.addVenueButtonText}>Add Your First Venue</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  venueList: {
    paddingHorizontal: 24,
    gap: 16,
  },
  venueCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  venueImage: {
    width: '100%',
    height: 160,
  },
  venueInfo: {
    padding: 16,
  },
  venueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  venueName: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginRight: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  locationText: {
    marginLeft: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    flex: 1,
  },
  venueStats: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  venueActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  editButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  bookingsCount: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 48,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  addVenueButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addVenueButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
});