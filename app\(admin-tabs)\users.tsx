import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { useData } from '@/contexts/DataContext';
import { userService } from '@/lib/database';
import { User, Mail, Phone, Calendar, Shield, UserCheck, CheckCircle, XCircle, Clock, AlertTriangle, Eye, Ban } from 'lucide-react-native';

export default function AdminUsers() {
  const { users, loading, approveUser, rejectUser, strikes, updateUser } = useData();
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [userDetailsVisible, setUserDetailsVisible] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  const handleApproveUser = async (userId: string, userName: string) => {
    Alert.alert(
      'Approve User',
      `Are you sure you want to approve ${userName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Approve',
          onPress: async () => {
            try {
              await approveUser(userId);
              Alert.alert('Success', 'User approved successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to approve user');
            }
          },
        },
      ]
    );
  };

  const handleRejectUser = async (userId: string, userName: string) => {
    Alert.alert(
      'Reject User',
      `Are you sure you want to reject ${userName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            try {
              await rejectUser(userId);
              Alert.alert('Success', 'User rejected successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to reject user');
            }
          },
        },
      ]
    );
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield size={20} color="#EF4444" />;
      case 'venue_owner':
        return <UserCheck size={20} color="#3B82F6" />;
      default:
        return <User size={20} color="#22C55E" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return '#EF4444';
      case 'venue_owner':
        return '#3B82F6';
      default:
        return '#22C55E';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Admin';
      case 'venue_owner':
        return 'Venue Owner';
      default:
        return 'Player';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle size={16} color="#22C55E" />;
      case 'rejected':
        return <XCircle size={16} color="#EF4444" />;
      case 'suspended':
        return <XCircle size={16} color="#F59E0B" />;
      default:
        return <Clock size={16} color="#6B7280" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#22C55E';
      case 'rejected':
        return '#EF4444';
      case 'suspended':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  const handleSuspendUser = (userId: string, userName: string) => {
    Alert.alert(
      'Suspend User',
      `Are you sure you want to suspend ${userName}? This will prevent them from accessing the platform.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Suspend',
          style: 'destructive',
          onPress: async () => {
            try {
              setActionLoading(true);
              await updateUser(userId, { status: 'suspended' });
              Alert.alert('Success', `${userName} has been suspended.`);
            } catch (error) {
              Alert.alert('Error', 'Failed to suspend user. Please try again.');
            } finally {
              setActionLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleViewUserDetails = (user: any) => {
    setSelectedUser(user);
    setUserDetailsVisible(true);
  };

  const getUserStrikes = (userId: string) => {
    return strikes.filter(strike =>
      strike.user_id === userId && strike.status === 'active'
    );
  };

  const renderUserCard = (user: any) => (
    <View key={user.id} style={styles.userCard}>
      <View style={styles.userHeader}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{user.name}</Text>
          <View style={styles.badgeContainer}>
            <View style={[styles.roleBadge, { backgroundColor: `${getRoleColor(user.user_type)}20` }]}>
              {getRoleIcon(user.user_type)}
              <Text style={[styles.roleText, { color: getRoleColor(user.user_type) }]}>
                {getRoleLabel(user.user_type)}
              </Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(user.status)}20` }]}>
              {getStatusIcon(user.status)}
              <Text style={[styles.statusText, { color: getStatusColor(user.status) }]}>
                {user.status?.charAt(0).toUpperCase() + user.status?.slice(1) || 'Pending'}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.userDetails}>
        <View style={styles.detailRow}>
          <Mail size={16} color="#6B7280" />
          <Text style={styles.detailText}>{user.email}</Text>
        </View>

        {user.phone && (
          <View style={styles.detailRow}>
            <Phone size={16} color="#6B7280" />
            <Text style={styles.detailText}>{user.phone}</Text>
          </View>
        )}

        {user.cnic && (
          <View style={styles.detailRow}>
            <User size={16} color="#6B7280" />
            <Text style={styles.detailText}>CNIC: {user.cnic}</Text>
          </View>
        )}

        <View style={styles.detailRow}>
          <Calendar size={16} color="#6B7280" />
          <Text style={styles.detailText}>
            Joined {new Date(user.created_at).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </Text>
        </View>

        {getUserStrikes(user.id).length > 0 && (
          <View style={styles.detailRow}>
            <AlertTriangle size={16} color="#EF4444" />
            <Text style={[styles.detailText, { color: '#EF4444' }]}>
              {getUserStrikes(user.id).length} active strike{getUserStrikes(user.id).length !== 1 ? 's' : ''}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.userActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.viewButton]}
          onPress={() => handleViewUserDetails(user)}
        >
          <Eye size={16} color="#3B82F6" />
          <Text style={[styles.actionButtonText, styles.viewButtonText]}>View Details</Text>
        </TouchableOpacity>

        {user.user_type !== 'admin' && (
          <>
            {user.status === 'pending' && (
              <>
                <TouchableOpacity
                  style={[styles.actionButton, styles.approveButton]}
                  onPress={() => handleApproveUser(user.id, user.name)}
                  disabled={actionLoading}
                >
                  <Text style={[styles.actionButtonText, styles.approveButtonText]}>Approve</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.actionButton, styles.dangerButton]}
                  onPress={() => handleRejectUser(user.id, user.name)}
                  disabled={actionLoading}
                >
                  <Text style={[styles.actionButtonText, styles.dangerButtonText]}>Reject</Text>
                </TouchableOpacity>
              </>
            )}
            {user.status === 'approved' && (
              <TouchableOpacity
                style={[styles.actionButton, styles.dangerButton]}
                onPress={() => handleSuspendUser(user.id, user.name)}
                disabled={actionLoading}
              >
                <Ban size={16} color="#EF4444" />
                <Text style={[styles.actionButtonText, styles.dangerButtonText]}>Suspend</Text>
              </TouchableOpacity>
            )}
          </>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>User Management</Text>
          <Text style={styles.subtitle}>Loading users...</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Management</Text>
        <Text style={styles.subtitle}>
          {users.length} registered user{users.length !== 1 ? 's' : ''}
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.userList}>
          {users.map(renderUserCard)}
        </View>
      </ScrollView>

      {/* User Details Modal */}
      <Modal
        visible={userDetailsVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setUserDetailsVisible(false)}>
              <Text style={styles.modalCloseText}>Close</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>User Details</Text>
            <View style={{ width: 50 }} />
          </View>

          {selectedUser && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.userDetailCard}>
                <View style={styles.userDetailHeader}>
                  <Text style={styles.userDetailName}>{selectedUser.name}</Text>
                  <View style={styles.userDetailBadges}>
                    <View style={[styles.roleBadge, { backgroundColor: `${getRoleColor(selectedUser.user_type)}20` }]}>
                      {getRoleIcon(selectedUser.user_type)}
                      <Text style={[styles.roleText, { color: getRoleColor(selectedUser.user_type) }]}>
                        {getRoleLabel(selectedUser.user_type)}
                      </Text>
                    </View>
                    <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(selectedUser.status)}20` }]}>
                      {getStatusIcon(selectedUser.status)}
                      <Text style={[styles.statusText, { color: getStatusColor(selectedUser.status) }]}>
                        {selectedUser.status?.charAt(0).toUpperCase() + selectedUser.status?.slice(1) || 'Pending'}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={styles.userDetailInfo}>
                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Contact Information</Text>
                    <View style={styles.detailRow}>
                      <Mail size={16} color="#6B7280" />
                      <Text style={styles.detailText}>{selectedUser.email}</Text>
                    </View>
                    {selectedUser.phone && (
                      <View style={styles.detailRow}>
                        <Phone size={16} color="#6B7280" />
                        <Text style={styles.detailText}>{selectedUser.phone}</Text>
                      </View>
                    )}
                    {selectedUser.address && (
                      <View style={styles.detailRow}>
                        <User size={16} color="#6B7280" />
                        <Text style={styles.detailText}>{selectedUser.address}</Text>
                      </View>
                    )}
                    {selectedUser.city && (
                      <View style={styles.detailRow}>
                        <User size={16} color="#6B7280" />
                        <Text style={styles.detailText}>{selectedUser.city}</Text>
                      </View>
                    )}
                    {selectedUser.cnic && (
                      <View style={styles.detailRow}>
                        <User size={16} color="#6B7280" />
                        <Text style={styles.detailText}>CNIC: {selectedUser.cnic}</Text>
                      </View>
                    )}
                  </View>

                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Account Information</Text>
                    <View style={styles.detailRow}>
                      <Calendar size={16} color="#6B7280" />
                      <Text style={styles.detailText}>
                        Joined {new Date(selectedUser.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Calendar size={16} color="#6B7280" />
                      <Text style={styles.detailText}>
                        Last updated {new Date(selectedUser.updated_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </Text>
                    </View>
                  </View>

                  {getUserStrikes(selectedUser.id).length > 0 && (
                    <View style={styles.detailSection}>
                      <Text style={styles.detailSectionTitle}>Active Strikes</Text>
                      {getUserStrikes(selectedUser.id).map((strike, index) => (
                        <View key={index} style={styles.strikeCard}>
                          <View style={styles.strikeHeader}>
                            <AlertTriangle size={16} color="#EF4444" />
                            <Text style={styles.strikeTitle}>{strike.reason}</Text>
                          </View>
                          {strike.description && (
                            <Text style={styles.strikeDescription}>{strike.description}</Text>
                          )}
                          <Text style={styles.strikeDate}>
                            Issued on {new Date(strike.created_at).toLocaleDateString()}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              </View>
            </ScrollView>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  userList: {
    paddingHorizontal: 24,
    gap: 16,
  },
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userHeader: {
    marginBottom: 16,
  },
  userInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  userName: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginRight: 12,
  },
  badgeContainer: {
    gap: 8,
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  roleText: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  approveButton: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  approveButtonText: {
    color: '#22C55E',
  },
  userDetails: {
    gap: 12,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    flex: 1,
  },
  userActions: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  actionButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  dangerButton: {
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  dangerButtonText: {
    color: '#EF4444',
  },
  viewButton: {
    borderColor: '#3B82F6',
    backgroundColor: '#EFF6FF',
  },
  viewButtonText: {
    color: '#3B82F6',
    marginLeft: 4,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  modalCloseText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  userDetailCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userDetailHeader: {
    marginBottom: 24,
  },
  userDetailName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 12,
  },
  userDetailBadges: {
    flexDirection: 'row',
    gap: 12,
  },
  userDetailInfo: {
    gap: 24,
  },
  detailSection: {
    gap: 12,
  },
  detailSectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  strikeCard: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  strikeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  strikeTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
    marginLeft: 8,
  },
  strikeDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#7F1D1D',
    marginBottom: 4,
  },
  strikeDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#991B1B',
  },
});