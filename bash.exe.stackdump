Stack trace:
Frame         Function      Args
0007FFFFBB60  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBB60, 0007FFFFAA60) msys-2.0.dll+0x2118E
0007FFFFBB60  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFBB60  0002100469F2 (00021028DF99, 0007FFFFBA18, 0007FFFFBB60, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBB60  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBB60  00021006A545 (0007FFFFBB70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBB70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF807C10000 ntdll.dll
7FFFD6900000 aswhook.dll
7FF805EB0000 KERNEL32.DLL
7FF8053C0000 KERNELBASE.dll
7FF8062D0000 USER32.dll
7FF805B00000 win32u.dll
7FF805F80000 GDI32.dll
7FF8052A0000 gdi32full.dll
7FF805870000 msvcp_win.dll
7FF805B30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8074E0000 advapi32.dll
7FF807A00000 msvcrt.dll
7FF807900000 sechost.dll
7FF806A10000 RPCRT4.dll
7FF8056C0000 bcrypt.dll
7FF804C40000 CRYPTBASE.DLL
7FF805A70000 bcryptPrimitives.dll
7FF806470000 IMM32.DLL
