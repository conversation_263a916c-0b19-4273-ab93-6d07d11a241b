import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { forumOfferService } from '@/lib/database';
import { ForumPost } from '@/types';
import { X, Users, MessageCircle } from 'lucide-react-native';

interface OfferModalProps {
  visible: boolean;
  onClose: () => void;
  forumPost: ForumPost;
  onOfferSubmitted: () => void;
}

export default function OfferModal({ visible, onClose, forumPost, onOfferSubmitted }: OfferModalProps) {
  const { user } = useAuth();
  const { addForumOffer } = useData();
  
  const [message, setMessage] = useState('');
  const [playersCount, setPlayersCount] = useState('1');
  const [skillLevel, setSkillLevel] = useState('');
  const [contactInfo, setContactInfo] = useState(user?.phone || '');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!user) return;

    if (!playersCount || parseInt(playersCount) < 1) {
      Alert.alert('Error', 'Please enter a valid number of players');
      return;
    }

    if (parseInt(playersCount) > forumPost.looking_for_players) {
      Alert.alert('Error', `Cannot offer more than ${forumPost.looking_for_players} players`);
      return;
    }

    try {
      setLoading(true);
      
      await addForumOffer({
        forum_post_id: forumPost.id,
        offering_player_id: user.id,
        message: message.trim() || undefined,
        players_count: parseInt(playersCount),
        skill_level: skillLevel || undefined,
        contact_info: contactInfo.trim() || undefined,
        status: 'pending'
      });

      Alert.alert(
        'Success',
        'Your offer has been sent successfully!',
        [{ text: 'OK', onPress: () => {
          onOfferSubmitted();
          onClose();
          resetForm();
        }}]
      );
    } catch (error) {
      console.error('Error creating offer:', error);
      Alert.alert('Error', 'Failed to send offer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setMessage('');
    setPlayersCount('1');
    setSkillLevel('');
    setContactInfo(user?.phone || '');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
          >
            <X size={24} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.title}>Make Offer</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Post Summary */}
          <View style={styles.postSummary}>
            <Text style={styles.postTitle}>{forumPost.title}</Text>
            <View style={styles.postDetails}>
              <View style={styles.postDetail}>
                <Users size={16} color="#22C55E" />
                <Text style={styles.postDetailText}>
                  Looking for {forumPost.looking_for_players} player{forumPost.looking_for_players > 1 ? 's' : ''}
                </Text>
              </View>
              <Text style={styles.postVenue}>
                {forumPost.booking?.venue?.name}
              </Text>
            </View>
          </View>

          {/* Offer Form */}
          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Number of Players *</Text>
              <TextInput
                style={styles.textInput}
                value={playersCount}
                onChangeText={setPlayersCount}
                placeholder="1"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
              />
              <Text style={styles.inputHint}>
                Maximum: {forumPost.looking_for_players} players
              </Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Message</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={message}
                onChangeText={setMessage}
                placeholder="Tell them about your team and why you'd be a good match..."
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Skill Level</Text>
              <View style={styles.skillLevelOptions}>
                {['Beginner', 'Intermediate', 'Advanced'].map((level) => (
                  <TouchableOpacity
                    key={level}
                    style={[
                      styles.skillOption,
                      skillLevel === level && styles.selectedSkillOption
                    ]}
                    onPress={() => setSkillLevel(skillLevel === level ? '' : level)}
                  >
                    <Text style={[
                      styles.skillOptionText,
                      skillLevel === level && styles.selectedSkillOptionText
                    ]}>
                      {level}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Contact Information</Text>
              <TextInput
                style={styles.textInput}
                value={contactInfo}
                onChangeText={setContactInfo}
                placeholder="Your phone number or preferred contact method"
                placeholderTextColor="#9CA3AF"
              />
              <Text style={styles.inputHint}>
                This will be shared with the post owner if your offer is accepted
              </Text>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={handleClose}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <MessageCircle size={20} color="#FFFFFF" />
            <Text style={styles.submitButtonText}>
              {loading ? 'Sending...' : 'Send Offer'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  postSummary: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  postTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  postDetails: {
    gap: 8,
  },
  postDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  postDetailText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#22C55E',
  },
  postVenue: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  form: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginTop: 12,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: 100,
  },
  inputHint: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  skillLevelOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  skillOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
  },
  selectedSkillOption: {
    borderColor: '#22C55E',
    backgroundColor: '#22C55E',
  },
  skillOptionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  selectedSkillOptionText: {
    color: '#FFFFFF',
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  submitButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#22C55E',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
