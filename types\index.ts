// Re-export types from supabase lib for consistency
export type { User, Venue, Booking, Review, Strike, VenueImage, TimeSlot } from '@/lib/supabase';

// Additional app-specific types
export interface SearchFilters {
  city?: string;
  priceRange?: [number, number];
  rating?: number;
  amenities?: string[];
}

export interface BookingRequest {
  venue_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_notes?: string;
}