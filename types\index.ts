// Re-export types from supabase lib for consistency
export type { User, Venue, Booking, Review, Strike, VenueImage, TimeSlot, ForumPost, ForumOffer } from '@/lib/supabase';

// Additional app-specific types
export interface SearchFilters {
  city?: string;
  priceRange?: [number, number];
  rating?: number;
  amenities?: string[];
}

export interface BookingRequest {
  venue_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_notes?: string;
}

export interface ForumPostRequest {
  booking_id: string;
  title: string;
  description?: string;
  looking_for_players: number;
  skill_level?: string;
  contact_preference: 'phone' | 'app' | 'both';
}

export interface ForumOfferRequest {
  forum_post_id: string;
  message?: string;
  players_count: number;
  skill_level?: string;
  contact_info?: string;
}

export interface ForumFilters {
  city?: string;
  skill_level?: string;
  date_range?: [string, string];
  players_needed?: number;
  status?: 'open' | 'closed';
}