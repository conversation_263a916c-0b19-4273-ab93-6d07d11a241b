import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { forumService } from '@/lib/database';
import { ForumPost } from '@/types';
import { Plus, Users, Calendar, MapPin, Clock, MessageCircle } from 'lucide-react-native';
import { useRouter } from 'expo-router';

export default function ForumScreen() {
  const { user } = useAuth();
  const { bookings, venues } = useData();
  const router = useRouter();
  const [forumPosts, setForumPosts] = useState<ForumPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Get user's confirmed bookings that can be posted to forum
  const eligibleBookings = bookings.filter(booking => 
    booking.player_id === user?.id && 
    booking.status === 'confirmed' &&
    new Date(booking.booking_date) >= new Date()
  );

  useEffect(() => {
    loadForumPosts();
  }, []);

  const loadForumPosts = async () => {
    try {
      setLoading(true);
      const posts = await forumService.getForumPosts({ status: 'open' });
      setForumPosts(posts || []);
    } catch (error) {
      console.error('Error loading forum posts:', error);
      Alert.alert('Error', 'Failed to load forum posts');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadForumPosts();
    setRefreshing(false);
  };

  const handleCreatePost = () => {
    if (eligibleBookings.length === 0) {
      Alert.alert(
        'No Eligible Bookings',
        'You need to have confirmed bookings to create forum posts. Make a booking first!',
        [{ text: 'OK' }]
      );
      return;
    }
    // Navigate to create post screen
    router.push('/forum/create-post');
  };

  const handlePostPress = (post: ForumPost) => {
    // Navigate to post details screen
    router.push(`/forum/post/${post.id}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const renderForumPost = (post: ForumPost) => {
    const venue = post.booking?.venue;
    
    return (
      <TouchableOpacity
        key={post.id}
        style={styles.postCard}
        onPress={() => handlePostPress(post)}
      >
        <View style={styles.postHeader}>
          <View style={styles.playerInfo}>
            <View style={styles.playerAvatar}>
              <Text style={styles.playerInitial}>
                {post.player?.name?.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.playerDetails}>
              <Text style={styles.playerName}>{post.player?.name}</Text>
              <Text style={styles.playerLocation}>{post.player?.city}</Text>
            </View>
          </View>
          <View style={styles.playersNeeded}>
            <Users size={16} color="#22C55E" />
            <Text style={styles.playersNeededText}>
              {post.looking_for_players} player{post.looking_for_players > 1 ? 's' : ''}
            </Text>
          </View>
        </View>

        <Text style={styles.postTitle}>{post.title}</Text>
        {post.description && (
          <Text style={styles.postDescription} numberOfLines={2}>
            {post.description}
          </Text>
        )}

        <View style={styles.bookingInfo}>
          <View style={styles.venueInfo}>
            <MapPin size={14} color="#6B7280" />
            <Text style={styles.venueText} numberOfLines={1}>
              {venue?.name}
            </Text>
          </View>
          
          <View style={styles.dateTimeInfo}>
            <View style={styles.dateInfo}>
              <Calendar size={14} color="#6B7280" />
              <Text style={styles.dateText}>
                {formatDate(post.booking?.booking_date || '')}
              </Text>
            </View>
            <View style={styles.timeInfo}>
              <Clock size={14} color="#6B7280" />
              <Text style={styles.timeText}>
                {formatTime(post.booking?.start_time || '')} - {formatTime(post.booking?.end_time || '')}
              </Text>
            </View>
          </View>
        </View>

        {post.skill_level && (
          <View style={styles.skillLevel}>
            <Text style={styles.skillLevelText}>Skill Level: {post.skill_level}</Text>
          </View>
        )}

        <View style={styles.postFooter}>
          <View style={styles.offersInfo}>
            <MessageCircle size={14} color="#6B7280" />
            <Text style={styles.offersText}>
              {post.offers_count || 0} offer{(post.offers_count || 0) !== 1 ? 's' : ''}
            </Text>
          </View>
          <Text style={styles.timeAgo}>
            {new Date(post.created_at).toLocaleDateString()}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.title}>Player Forum</Text>
          <Text style={styles.subtitle}>
            Find teammates for your bookings
          </Text>
        </View>
        <TouchableOpacity
          style={styles.createButton}
          onPress={handleCreatePost}
        >
          <Plus size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading forum posts...</Text>
          </View>
        ) : forumPosts.length > 0 ? (
          <View style={styles.postsList}>
            {forumPosts.map(renderForumPost)}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Users size={48} color="#D1D5DB" />
            <Text style={styles.emptyStateTitle}>No posts yet</Text>
            <Text style={styles.emptyStateText}>
              Be the first to post your booking and find teammates!
            </Text>
            <TouchableOpacity
              style={styles.emptyStateButton}
              onPress={handleCreatePost}
            >
              <Text style={styles.emptyStateButtonText}>Create Post</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  createButton: {
    backgroundColor: '#22C55E',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  postsList: {
    padding: 16,
  },
  postCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  playerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  playerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#22C55E',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  playerInitial: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  playerDetails: {
    flex: 1,
  },
  playerName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  playerLocation: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  playersNeeded: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#22C55E20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  playersNeededText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#22C55E',
    marginLeft: 4,
  },
  postTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  postDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  bookingInfo: {
    marginBottom: 12,
  },
  venueInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  venueText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginLeft: 6,
    flex: 1,
  },
  dateTimeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  timeText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  skillLevel: {
    marginBottom: 12,
  },
  skillLevelText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
    backgroundColor: '#3B82F620',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  postFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  offersInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  offersText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  timeAgo: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: '#22C55E',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
