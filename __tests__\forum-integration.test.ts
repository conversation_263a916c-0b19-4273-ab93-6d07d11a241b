/**
 * Forum Feature Integration Tests
 * 
 * These tests verify the complete forum workflow from post creation to offer management.
 * They test the integration between different components and services.
 */

import { forumService, forumOfferService } from '@/lib/database';

// Mock data for testing
const mockUser1 = { id: 'user-1', name: '<PERSON>' };
const mockUser2 = { id: 'user-2', name: '<PERSON>' };
const mockBooking = {
  id: 'booking-1',
  player_id: 'user-1',
  status: 'confirmed',
  booking_date: '2024-12-31'
};

describe('Forum Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Forum Workflow', () => {
    test('should handle complete post creation and offer workflow', async () => {
      // Step 1: Create a forum post
      const postData = {
        booking_id: mockBooking.id,
        player_id: mockUser1.id,
        title: 'Looking for teammates for evening match',
        description: 'Need 2 skilled players for a competitive match',
        looking_for_players: 2,
        skill_level: 'Intermediate',
        contact_preference: 'phone' as const,
        is_active: true,
        status: 'open' as const
      };

      // Mock successful post creation
      jest.spyOn(forumService, 'createForumPost').mockResolvedValue({
        ...postData,
        id: 'post-1',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      });

      const createdPost = await forumService.createForumPost(postData);
      expect(createdPost.id).toBe('post-1');
      expect(createdPost.title).toBe(postData.title);

      // Step 2: Another user makes an offer
      const offerData = {
        forum_post_id: createdPost.id,
        offering_player_id: mockUser2.id,
        message: 'We are a team of 2 intermediate players, would love to join!',
        players_count: 2,
        skill_level: 'Intermediate',
        contact_info: '+1234567890',
        status: 'pending' as const
      };

      jest.spyOn(forumOfferService, 'createOffer').mockResolvedValue({
        ...offerData,
        id: 'offer-1',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      });

      const createdOffer = await forumOfferService.createOffer(offerData);
      expect(createdOffer.id).toBe('offer-1');
      expect(createdOffer.message).toBe(offerData.message);

      // Step 3: Post owner accepts the offer
      jest.spyOn(forumOfferService, 'updateOfferStatus').mockResolvedValue({
        ...createdOffer,
        status: 'accepted',
        responded_at: '2023-01-01T01:00:00Z',
        response_message: 'Offer accepted!'
      });

      const acceptedOffer = await forumOfferService.updateOfferStatus(
        createdOffer.id,
        'accepted',
        'Offer accepted!'
      );
      expect(acceptedOffer.status).toBe('accepted');

      // Step 4: Verify post is closed
      jest.spyOn(forumService, 'closeForumPost').mockResolvedValue({
        ...createdPost,
        status: 'closed',
        is_active: false,
        closed_at: '2023-01-01T01:00:00Z'
      });

      const closedPost = await forumService.closeForumPost(createdPost.id);
      expect(closedPost.status).toBe('closed');
      expect(closedPost.is_active).toBe(false);
    });

    test('should handle offer rejection workflow', async () => {
      const offerId = 'offer-2';
      
      jest.spyOn(forumOfferService, 'updateOfferStatus').mockResolvedValue({
        id: offerId,
        forum_post_id: 'post-1',
        offering_player_id: 'user-2',
        players_count: 1,
        status: 'rejected',
        responded_at: '2023-01-01T01:00:00Z',
        response_message: 'Thanks for the offer, but we found other players.',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T01:00:00Z'
      });

      const rejectedOffer = await forumOfferService.updateOfferStatus(
        offerId,
        'rejected',
        'Thanks for the offer, but we found other players.'
      );

      expect(rejectedOffer.status).toBe('rejected');
      expect(rejectedOffer.response_message).toContain('Thanks for the offer');
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle cascading errors gracefully', async () => {
      // Test what happens when a post is deleted while offers exist
      const postId = 'post-to-delete';
      
      jest.spyOn(forumService, 'deleteForumPost').mockResolvedValue(true);
      
      const result = await forumService.deleteForumPost(postId);
      expect(result).toBe(true);
      
      // Offers should be automatically deleted due to CASCADE constraint
      jest.spyOn(forumOfferService, 'getOffersForPost').mockResolvedValue([]);
      
      const offers = await forumOfferService.getOffersForPost(postId);
      expect(offers).toHaveLength(0);
    });

    test('should handle concurrent offer acceptance', async () => {
      // Simulate two offers being accepted simultaneously
      const postId = 'post-concurrent';
      const offer1Id = 'offer-1';
      const offer2Id = 'offer-2';

      // First offer gets accepted
      jest.spyOn(forumOfferService, 'updateOfferStatus').mockResolvedValueOnce({
        id: offer1Id,
        forum_post_id: postId,
        offering_player_id: 'user-1',
        players_count: 1,
        status: 'accepted',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T01:00:00Z'
      });

      // Second offer should be automatically rejected
      jest.spyOn(forumOfferService, 'rejectOtherOffers').mockResolvedValue(true);

      await forumOfferService.updateOfferStatus(offer1Id, 'accepted');
      const rejectResult = await forumOfferService.rejectOtherOffers(postId, offer1Id);
      
      expect(rejectResult).toBe(true);
    });
  });

  describe('Data Consistency Tests', () => {
    test('should maintain data consistency across operations', async () => {
      // Test that forum post and booking data remain consistent
      const postData = {
        booking_id: 'booking-consistency-test',
        player_id: 'user-consistency',
        title: 'Consistency Test Post',
        looking_for_players: 1,
        contact_preference: 'phone' as const,
        is_active: true,
        status: 'open' as const
      };

      jest.spyOn(forumService, 'createForumPost').mockResolvedValue({
        ...postData,
        id: 'post-consistency',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      });

      const post = await forumService.createForumPost(postData);
      
      // Verify that the post references the correct booking
      expect(post.booking_id).toBe(postData.booking_id);
      expect(post.player_id).toBe(postData.player_id);
    });

    test('should handle database constraint violations', async () => {
      // Test unique constraint on booking_id
      const duplicatePostData = {
        booking_id: 'existing-booking',
        player_id: 'user-1',
        title: 'Duplicate Post',
        looking_for_players: 1,
        contact_preference: 'phone' as const,
        is_active: true,
        status: 'open' as const
      };

      jest.spyOn(forumService, 'createForumPost')
        .mockRejectedValue(new Error('A forum post already exists for this booking'));

      await expect(forumService.createForumPost(duplicatePostData))
        .rejects.toThrow('A forum post already exists for this booking');
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large numbers of offers efficiently', async () => {
      const postId = 'popular-post';
      const manyOffers = Array.from({ length: 100 }, (_, i) => ({
        id: `offer-${i}`,
        forum_post_id: postId,
        offering_player_id: `user-${i}`,
        players_count: 1,
        status: 'pending' as const,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }));

      jest.spyOn(forumOfferService, 'getOffersForPost').mockResolvedValue(manyOffers);

      const startTime = Date.now();
      const offers = await forumOfferService.getOffersForPost(postId);
      const endTime = Date.now();

      expect(offers).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});

describe('Business Rules Validation', () => {
  test('should enforce business rules correctly', () => {
    // Test 1: Players cannot offer more than required
    const postRequirement = 2;
    const offerAmount = 3;
    expect(offerAmount > postRequirement).toBe(true);
    // This should be caught by validation

    // Test 2: Players cannot offer on their own posts
    const postOwnerId = 'player-123';
    const offeringPlayerId = 'player-123';
    expect(postOwnerId === offeringPlayerId).toBe(true);
    // This should be caught by validation

    // Test 3: Cannot make offers on closed posts
    const postStatus = 'closed';
    expect(postStatus !== 'open').toBe(true);
    // This should be caught by validation

    // Test 4: Cannot create posts for past bookings
    const bookingDate = new Date('2023-01-01');
    const currentDate = new Date();
    expect(bookingDate < currentDate).toBe(true);
    // This should be caught by validation
  });
});
