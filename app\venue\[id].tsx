import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  TextInput,
  Modal,
  Clipboard,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useData } from '@/contexts/DataContext';
import { useAuth } from '@/contexts/AuthContext';
import { bookingService, reviewService, strikeService } from '@/lib/database';
import { ArrowLeft, Star, MapPin, DollarSign, Users, Clock, Calendar, Phone, Plus, Minus, CreditCard, AlertTriangle, MessageCircle, Copy, X } from 'lucide-react-native';

export default function VenueDetails() {
  const { id } = useLocalSearchParams();
  const { venues, addBooking, bookings } = useData();
  const { user } = useAuth();
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [duration, setDuration] = useState(1);
  const [playerNotes, setPlayerNotes] = useState('');
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [existingBookings, setExistingBookings] = useState<any[]>([]);
  const [venueReviews, setVenueReviews] = useState<any[]>([]);
  const [venueStrikes, setVenueStrikes] = useState<any[]>([]);
  const [showAllReviews, setShowAllReviews] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewRating, setReviewRating] = useState(5);
  const [reviewComment, setReviewComment] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);

  const venue = venues.find(v => v.id === id);

  useEffect(() => {
    if (venue) {
      fetchVenueData();
      if (selectedDate) {
        fetchExistingBookings();
      }
    }
  }, [venue, selectedDate]);

  const fetchVenueData = async () => {
    try {
      // Fetch reviews
      const reviews = await reviewService.getReviewsForVenue(venue!.id);
      setVenueReviews(reviews || []);

      // Fetch strikes
      const strikes = await strikeService.getStrikes(undefined, venue!.id);
      setVenueStrikes(strikes || []);
    } catch (error) {
      console.error('Error fetching venue data:', error);
    }
  };

  const fetchExistingBookings = async () => {
    try {
      const venueBookings = await bookingService.getBookings(undefined, venue!.id);
      const dateBookings = venueBookings.filter(booking =>
        booking.booking_date === selectedDate &&
        (booking.status === 'confirmed' || booking.status === 'pending')
      );
      setExistingBookings(dateBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
    }
  };

  if (!venue) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Venue not found</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Generate available time slots based on venue hours
  const generateTimeSlots = () => {
    const slots = [];
    const openingHour = parseInt(venue.opening_time.split(':')[0]);
    const closingHour = parseInt(venue.closing_time.split(':')[0]);

    for (let hour = openingHour; hour < closingHour; hour++) {
      const timeSlot = `${hour.toString().padStart(2, '0')}:00`;
      slots.push(timeSlot);
    }

    return slots;
  };

  // Calculate pricing based on time and date - handles variable pricing across hours
  const calculatePrice = () => {
    if (!selectedDate || !selectedTime) return 0;

    const startHour = parseInt(selectedTime.split(':')[0]);
    let totalPrice = 0;

    // Calculate price for each hour in the duration
    for (let i = 0; i < duration; i++) {
      const currentHour = startHour + i;
      const currentTime = `${currentHour.toString().padStart(2, '0')}:00`;
      const hourPrice = getPricePerHour(currentTime, selectedDate);
      totalPrice += hourPrice;
    }

    return totalPrice;
  };

  // Get price per hour for a specific time
  const getPricePerHour = (time: string, date: string) => {
    const bookingDate = new Date(date);
    const isWeekend = bookingDate.getDay() === 0 || bookingDate.getDay() === 6;
    const hour = parseInt(time.split(':')[0]);
    const nightTimeStart = parseInt(venue.night_time_start.split(':')[0]);
    const isNightTime = hour >= nightTimeStart;

    if (isWeekend && isNightTime && venue.weekend_night_price) {
      return venue.weekend_night_price;
    } else if (isWeekend && venue.weekend_day_price) {
      return venue.weekend_day_price;
    } else if (isNightTime) {
      return venue.night_price_per_hour;
    }

    return venue.day_price_per_hour;
  };

  // Check if time slot is available for the selected duration
  const isTimeSlotAvailable = (time: string) => {
    const startHour = parseInt(time.split(':')[0]);
    const endHour = startHour + duration;
    const endTime = `${endHour.toString().padStart(2, '0')}:${time.split(':')[1]}`;

    // Check if the end time exceeds venue closing time
    const closingHour = parseInt(venue.closing_time.split(':')[0]);
    if (endHour > closingHour) {
      return false;
    }

    // Check for conflicts with existing bookings
    return !existingBookings.some(booking => {
      const bookingStartHour = parseInt(booking.start_time.split(':')[0]);
      const bookingEndHour = parseInt(booking.end_time.split(':')[0]);

      // Check for any overlap between the requested time range and existing bookings
      return (startHour < bookingEndHour && endHour > bookingStartHour);
    });
  };

  // Check if all consecutive slots are available for multi-hour booking
  const areConsecutiveSlotsAvailable = (startTime: string, duration: number) => {
    const startHour = parseInt(startTime.split(':')[0]);

    for (let i = 0; i < duration; i++) {
      const currentHour = startHour + i;
      const currentTime = `${currentHour.toString().padStart(2, '0')}:00`;

      // Check if this individual hour slot is available
      const hasConflict = existingBookings.some(booking => {
        const bookingStartHour = parseInt(booking.start_time.split(':')[0]);
        const bookingEndHour = parseInt(booking.end_time.split(':')[0]);
        return currentHour >= bookingStartHour && currentHour < bookingEndHour;
      });

      if (hasConflict) {
        return false;
      }
    }

    return true;
  };

  const handleBooking = () => {
    if (!selectedDate || !selectedTime) {
      Alert.alert('Error', 'Please select a date and time for your booking');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'Please sign in to make a booking');
      return;
    }

    if (!areConsecutiveSlotsAvailable(selectedTime, duration)) {
      Alert.alert(
        'Time Slot Unavailable',
        `The selected ${duration} hour${duration > 1 ? 's' : ''} starting at ${selectedTime} is not available. Some slots in this time range are already booked.`
      );
      return;
    }

    setShowBookingModal(true);
  };

  const confirmBooking = async () => {
    try {
      setLoading(true);

      const bookingDate = new Date(selectedDate);
      const isWeekend = bookingDate.getDay() === 0 || bookingDate.getDay() === 6;
      const hour = parseInt(selectedTime.split(':')[0]);
      const nightTimeStart = parseInt(venue.night_time_start.split(':')[0]);
      const isNightTime = hour >= nightTimeStart;
      const endTime = `${parseInt(selectedTime.split(':')[0]) + duration}:${selectedTime.split(':')[1]}`;
      const totalAmount = calculatePrice();

      let pricePerHour = venue.day_price_per_hour;
      if (isWeekend && isNightTime && venue.weekend_night_price) {
        pricePerHour = venue.weekend_night_price;
      } else if (isWeekend && venue.weekend_day_price) {
        pricePerHour = venue.weekend_day_price;
      } else if (isNightTime) {
        pricePerHour = venue.night_price_per_hour;
      }

      await addBooking({
        player_id: user.id,
        venue_id: venue.id,
        booking_date: selectedDate,
        start_time: selectedTime,
        end_time: endTime,
        total_hours: duration,
        price_per_hour: pricePerHour,
        total_amount: totalAmount,
        is_weekend: isWeekend,
        is_night_time: isNightTime,
        status: 'pending',
        payment_status: 'pending',
        player_notes: playerNotes,
      });

      setShowBookingModal(false);
      Alert.alert(
        'Booking Submitted',
        'Your booking request has been submitted. The venue owner will confirm your booking shortly.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Generate next 7 days for date selection
  const getNextDays = () => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      days.push({
        date: date.toISOString().split('T')[0],
        label: date.toLocaleDateString('en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        })
      });
    }
    return days;
  };

  const availableDays = getNextDays();
  const availableTimeSlots = generateTimeSlots();

  // Check if player can review this venue
  const canReviewVenue = () => {
    if (!user) return false;

    // Check if player has completed bookings at this venue
    const playerCompletedBookings = existingBookings.filter(booking =>
      booking.player_id === user.id &&
      booking.status === 'completed'
    );

    if (playerCompletedBookings.length === 0) return false;

    // Check if player has already reviewed this venue
    const existingReview = venueReviews.find(review =>
      review.reviewer_id === user.id &&
      review.review_type === 'player_to_venue'
    );

    return !existingReview;
  };

  // Helper function for strike severity colors
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return '#F59E0B';
      case 'medium': return '#EF4444';
      case 'high': return '#DC2626';
      case 'critical': return '#991B1B';
      default: return '#6B7280';
    }
  };

  // Submit review function
  const handleSubmitReview = async () => {
    if (!user || !venue) return;

    try {
      setLoading(true);

      const reviewData = {
        reviewer_id: user.id,
        venue_id: venue.id,
        reviewee_id: venue.owner_id,
        review_type: 'player_to_venue' as const,
        rating: reviewRating,
        comment: reviewComment.trim() || undefined,
        is_anonymous: isAnonymous,
      };

      await reviewService.createReview(reviewData);

      // Refresh venue reviews
      const updatedReviews = await reviewService.getReviewsForVenue(venue.id);
      setVenueReviews(updatedReviews || []);

      // Reset form and close modal
      setReviewRating(5);
      setReviewComment('');
      setIsAnonymous(false);
      setShowReviewModal(false);

      Alert.alert('Success', 'Your review has been submitted successfully!');
    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Copy phone number to clipboard
  const copyPhoneNumber = async (phoneNumber: string) => {
    try {
      await Clipboard.setString(phoneNumber);
      Alert.alert('Copied!', 'Phone number copied to clipboard');
    } catch (error) {
      Alert.alert('Error', 'Failed to copy phone number');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: venue.images?.[0] || 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg' }}
            style={styles.venueImage}
            resizeMode="cover"
          />
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.venueInfo}>
          <View style={styles.venueHeader}>
            <Text style={styles.venueName}>{venue.name}</Text>
            <View style={styles.ratingContainer}>
              <Star size={16} color="#F59E0B" fill="#F59E0B" />
              <Text style={styles.rating}>{venue.rating}</Text>
              <Text style={styles.ratingCount}>({venue.total_bookings} bookings)</Text>
            </View>
          </View>

          <View style={styles.venueLocation}>
            <MapPin size={16} color="#6B7280" />
            <Text style={styles.locationText}>{venue.address}, {venue.city}</Text>
          </View>

          <View style={styles.venueStats}>
            <View style={styles.statItem}>
              <DollarSign size={16} color="#22C55E" />
              <Text style={styles.statText}>PKR {venue.day_price_per_hour}/hour (Day)</Text>
            </View>
            <View style={styles.statItem}>
              <Users size={16} color="#3B82F6" />
              <Text style={styles.statText}>Up to {venue.capacity || 22} people</Text>
            </View>
          </View>

          {/* Pricing Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pricing</Text>
            <View style={styles.pricingGrid}>
              <View style={styles.pricingCard}>
                <Text style={styles.pricingLabel}>Weekday Day</Text>
                <Text style={styles.pricingValue}>PKR {venue.day_price_per_hour}/hr</Text>
                <Text style={styles.pricingTime}>Before {venue.night_time_start}</Text>
              </View>
              <View style={styles.pricingCard}>
                <Text style={styles.pricingLabel}>Weekday Night</Text>
                <Text style={styles.pricingValue}>PKR {venue.night_price_per_hour}/hr</Text>
                <Text style={styles.pricingTime}>After {venue.night_time_start}</Text>
              </View>
              {venue.weekend_day_price && (
                <View style={styles.pricingCard}>
                  <Text style={styles.pricingLabel}>Weekend Day</Text>
                  <Text style={styles.pricingValue}>PKR {venue.weekend_day_price}/hr</Text>
                  <Text style={styles.pricingTime}>Sat-Sun before {venue.night_time_start}</Text>
                </View>
              )}
              {venue.weekend_night_price && (
                <View style={styles.pricingCard}>
                  <Text style={styles.pricingLabel}>Weekend Night</Text>
                  <Text style={styles.pricingValue}>PKR {venue.weekend_night_price}/hr</Text>
                  <Text style={styles.pricingTime}>Sat-Sun after {venue.night_time_start}</Text>
                </View>
              )}
            </View>
          </View>

          {/* Strikes Information */}
          {venueStrikes.length > 0 && (
            <View style={styles.section}>
              <View style={styles.strikesHeader}>
                <AlertTriangle size={18} color="#EF4444" />
                <Text style={styles.sectionTitle}>Venue Strikes ({venue.strikes})</Text>
              </View>
              <View style={styles.strikesContainer}>
                {venueStrikes.slice(0, 3).map((strike, index) => (
                  <View key={index} style={styles.strikeCard}>
                    <View style={styles.strikeHeader}>
                      <Text style={styles.strikeType}>{strike.strike_type}</Text>
                      <Text style={[styles.strikeSeverity, { color: getSeverityColor(strike.severity) }]}>
                        {strike.severity.toUpperCase()}
                      </Text>
                    </View>
                    <Text style={styles.strikeReason}>{strike.reason}</Text>
                    <Text style={styles.strikeDate}>
                      {new Date(strike.created_at).toLocaleDateString()}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{venue.description}</Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Amenities</Text>
            <View style={styles.amenitiesContainer}>
              {(venue.amenities || []).map((amenity, index) => (
                <View key={index} style={styles.amenityTag}>
                  <Text style={styles.amenityText}>{amenity}</Text>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Available Hours</Text>
            <View style={styles.hoursContainer}>
              {(venue.available_hours || []).map((hour, index) => (
                <View key={index} style={styles.hourTag}>
                  <Clock size={14} color="#6B7280" />
                  <Text style={styles.hourText}>{hour}</Text>
                </View>
              ))}
            </View>
          </View>

          {user?.user_type === 'player' && (
            <View style={styles.bookingSection}>
              <Text style={styles.sectionTitle}>Book This Venue</Text>

              <View style={styles.dateSelection}>
                <Text style={styles.selectionLabel}>Select Date:</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.dateScroll}>
                  {availableDays.map((day) => (
                    <TouchableOpacity
                      key={day.date}
                      style={[
                        styles.dateButton,
                        selectedDate === day.date && styles.dateButtonActive
                      ]}
                      onPress={() => setSelectedDate(day.date)}
                    >
                      <Text style={[
                        styles.dateButtonText,
                        selectedDate === day.date && styles.dateButtonTextActive
                      ]}>
                        {day.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              <View style={styles.durationSelection}>
                <Text style={styles.selectionLabel}>Duration:</Text>
                <View style={styles.durationControls}>
                  <TouchableOpacity
                    style={[styles.durationButton, duration <= 1 && styles.durationButtonDisabled]}
                    onPress={() => {
                      if (duration > 1) {
                        setDuration(duration - 1);
                        // If selected time becomes invalid with new duration, clear it
                        if (selectedTime && selectedDate && !areConsecutiveSlotsAvailable(selectedTime, duration - 1)) {
                          setSelectedTime('');
                        }
                      }
                    }}
                    disabled={duration <= 1}
                  >
                    <Minus size={16} color={duration <= 1 ? "#D1D5DB" : "#6B7280"} />
                  </TouchableOpacity>
                  <Text style={styles.durationText}>{duration} hour{duration > 1 ? 's' : ''}</Text>
                  <TouchableOpacity
                    style={styles.durationButton}
                    onPress={() => {
                      const newDuration = duration + 1;
                      setDuration(newDuration);
                      // If selected time becomes invalid with new duration, clear it
                      if (selectedTime && selectedDate && !areConsecutiveSlotsAvailable(selectedTime, newDuration)) {
                        setSelectedTime('');
                      }
                    }}
                  >
                    <Plus size={16} color="#6B7280" />
                  </TouchableOpacity>
                </View>
                {selectedTime && duration > 1 && (
                  <Text style={styles.durationInfo}>
                    Selected: {selectedTime} - {parseInt(selectedTime.split(':')[0]) + duration}:00
                  </Text>
                )}
              </View>

              <View style={styles.timeSelection}>
                <Text style={styles.selectionLabel}>Select Time:</Text>
                {duration > 1 && (
                  <Text style={styles.durationNote}>
                    Selecting a time will book {duration} consecutive hours
                  </Text>
                )}
                <View style={styles.timeGrid}>
                  {availableTimeSlots.map((hour) => {
                    const available = selectedDate ? areConsecutiveSlotsAvailable(hour, duration) : true;
                    const pricePerHour = selectedDate ? getPricePerHour(hour, selectedDate) : venue.day_price_per_hour;

                    // Check if this would exceed closing time
                    const startHour = parseInt(hour.split(':')[0]);
                    const endHour = startHour + duration;
                    const closingHour = parseInt(venue.closing_time.split(':')[0]);
                    const exceedsClosing = endHour > closingHour;

                    const isAvailable = available && !exceedsClosing;

                    // Check if this slot is part of the selected range
                    const isInSelectedRange = selectedTime && duration > 1 && (() => {
                      const selectedStartHour = parseInt(selectedTime.split(':')[0]);
                      const currentHour = parseInt(hour.split(':')[0]);
                      return currentHour >= selectedStartHour && currentHour < selectedStartHour + duration;
                    })();

                    // Check if this is the start of the selected range
                    const isRangeStart = selectedTime === hour;

                    // Check if this is the end of the selected range
                    const isRangeEnd = selectedTime && duration > 1 && (() => {
                      const selectedStartHour = parseInt(selectedTime.split(':')[0]);
                      const currentHour = parseInt(hour.split(':')[0]);
                      return currentHour === selectedStartHour + duration - 1;
                    })();

                    return (
                      <TouchableOpacity
                        key={hour}
                        style={[
                          styles.timeButton,
                          isRangeStart && styles.timeButtonActive,
                          isInSelectedRange && !isRangeStart && styles.timeButtonInRange,
                          isRangeEnd && styles.timeButtonRangeEnd,
                          !isAvailable && styles.timeButtonUnavailable
                        ]}
                        onPress={() => isAvailable && setSelectedTime(hour)}
                        disabled={!isAvailable}
                      >
                        <Text style={[
                          styles.timeButtonText,
                          isRangeStart && styles.timeButtonTextActive,
                          isInSelectedRange && !isRangeStart && styles.timeButtonTextInRange,
                          !isAvailable && styles.timeButtonTextUnavailable
                        ]}>
                          {hour}
                        </Text>
                        <Text style={[
                          styles.timeButtonPrice,
                          isRangeStart && styles.timeButtonPriceActive,
                          isInSelectedRange && !isRangeStart && styles.timeButtonPriceInRange,
                          !isAvailable && styles.timeButtonTextUnavailable
                        ]}>
                          PKR {pricePerHour}
                        </Text>
                        {isInSelectedRange && duration > 1 && (
                          <View style={styles.rangeIndicator}>
                            <Text style={styles.rangeIndicatorText}>
                              {isRangeStart ? 'START' : isRangeEnd ? 'END' : 'SELECTED'}
                            </Text>
                          </View>
                        )}
                        {!isAvailable && exceedsClosing && (
                          <Text style={styles.timeButtonNote}>
                            Exceeds closing
                          </Text>
                        )}
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>

              {selectedDate && selectedTime && (
                <View style={styles.pricingInfo}>
                  <View style={styles.pricingRow}>
                    <Text style={styles.pricingLabel}>Duration:</Text>
                    <Text style={styles.pricingValue}>{duration} hour{duration > 1 ? 's' : ''}</Text>
                  </View>
                  <View style={styles.pricingRow}>
                    <Text style={styles.pricingLabel}>Time Range:</Text>
                    <Text style={styles.pricingValue}>
                      {selectedTime} - {parseInt(selectedTime.split(':')[0]) + duration}:00
                    </Text>
                  </View>
                  {duration > 1 && (
                    <View style={styles.pricingBreakdown}>
                      <Text style={styles.pricingLabel}>Hourly Breakdown:</Text>
                      {Array.from({ length: duration }, (_, i) => {
                        const hourStart = parseInt(selectedTime.split(':')[0]) + i;
                        const hourTime = `${hourStart.toString().padStart(2, '0')}:00`;
                        const hourPrice = getPricePerHour(hourTime, selectedDate);
                        return (
                          <View key={i} style={styles.hourBreakdownRow}>
                            <Text style={styles.hourBreakdownTime}>
                              {hourTime} - {(hourStart + 1).toString().padStart(2, '0')}:00
                            </Text>
                            <Text style={styles.hourBreakdownPrice}>PKR {hourPrice}</Text>
                          </View>
                        );
                      })}
                    </View>
                  )}
                  <View style={[styles.pricingRow, styles.totalRow]}>
                    <Text style={styles.totalLabel}>Total:</Text>
                    <Text style={styles.totalValue}>PKR {calculatePrice()}</Text>
                  </View>
                </View>
              )}

              <TouchableOpacity
                style={[styles.bookButton, (!selectedDate || !selectedTime) && styles.bookButtonDisabled]}
                onPress={handleBooking}
                disabled={!selectedDate || !selectedTime}
              >
                <Calendar size={20} color="#FFFFFF" />
                <Text style={styles.bookButtonText}>
                  {selectedDate && selectedTime ? `Book Now - PKR ${calculatePrice()}` : 'Select Date & Time'}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Reviews Section */}
          <View style={styles.section}>
            <View style={styles.reviewsHeader}>
              <Text style={styles.sectionTitle}>Reviews ({venueReviews.length})</Text>
              <View style={styles.reviewsActions}>
                {canReviewVenue() && (
                  <TouchableOpacity
                    style={styles.addReviewButton}
                    onPress={() => setShowReviewModal(true)}
                  >
                    <Plus size={16} color="#FFFFFF" />
                    <Text style={styles.addReviewText}>Add Review</Text>
                  </TouchableOpacity>
                )}
                {venueReviews.length > 3 && (
                  <TouchableOpacity onPress={() => setShowAllReviews(!showAllReviews)}>
                    <Text style={styles.showMoreText}>
                      {showAllReviews ? 'Show Less' : 'Show All'}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>

            {venueReviews.length > 0 ? (
              <View style={styles.reviewsContainer}>
                {(showAllReviews ? venueReviews : venueReviews.slice(0, 3)).map((review, index) => (
                  <View key={index} style={styles.reviewCard}>
                    <View style={styles.reviewHeader}>
                      <View style={styles.reviewerInfo}>
                        <Text style={styles.reviewerName}>
                          {review.is_anonymous ? 'Anonymous Player' : review.reviewer?.name || 'Player'}
                        </Text>
                        <View style={styles.reviewRating}>
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              size={12}
                              color={i < review.rating ? "#F59E0B" : "#E5E7EB"}
                              fill={i < review.rating ? "#F59E0B" : "#E5E7EB"}
                            />
                          ))}
                          <Text style={styles.reviewRatingText}>({review.rating})</Text>
                        </View>
                      </View>
                      <Text style={styles.reviewDate}>
                        {new Date(review.created_at).toLocaleDateString()}
                      </Text>
                    </View>
                    {review.comment && (
                      <Text style={styles.reviewComment}>{review.comment}</Text>
                    )}
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.noReviewsContainer}>
                <MessageCircle size={48} color="#D1D5DB" />
                <Text style={styles.noReviewsText}>No reviews yet</Text>
                <Text style={styles.noReviewsSubtext}>Be the first to book and review this venue!</Text>
              </View>
            )}
          </View>

          <View style={styles.contactSection}>
            <Text style={styles.sectionTitle}>Contact Venue</Text>
            <TouchableOpacity
              style={styles.contactButton}
              onPress={() => setShowContactModal(true)}
            >
              <Phone size={20} color="#3B82F6" />
              <Text style={styles.contactButtonText}>Contact Venue Owner</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Booking Confirmation Modal */}
      <Modal
        visible={showBookingModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowBookingModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Confirm Booking</Text>
            <TouchableOpacity onPress={confirmBooking} disabled={loading}>
              <Text style={[styles.modalConfirmText, loading && { opacity: 0.5 }]}>
                {loading ? 'Booking...' : 'Confirm'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.bookingSummary}>
              <Text style={styles.summaryTitle}>Booking Summary</Text>

              <View style={styles.summaryCard}>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Venue:</Text>
                  <Text style={styles.summaryValue}>{venue.name}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Date:</Text>
                  <Text style={styles.summaryValue}>
                    {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Time:</Text>
                  <Text style={styles.summaryValue}>
                    {selectedTime} - {parseInt(selectedTime.split(':')[0]) + duration}:00
                  </Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Duration:</Text>
                  <Text style={styles.summaryValue}>{duration} hour{duration > 1 ? 's' : ''}</Text>
                </View>
                <View style={[styles.summaryRow, styles.totalSummaryRow]}>
                  <Text style={styles.summaryTotalLabel}>Total Amount:</Text>
                  <Text style={styles.summaryTotalValue}>PKR {calculatePrice()}</Text>
                </View>
              </View>

              <View style={styles.notesSection}>
                <Text style={styles.notesLabel}>Additional Notes (Optional):</Text>
                <TextInput
                  style={styles.notesInput}
                  value={playerNotes}
                  onChangeText={setPlayerNotes}
                  placeholder="Any special requests or notes for the venue owner..."
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.paymentInfo}>
                <View style={styles.paymentHeader}>
                  <CreditCard size={20} color="#3B82F6" />
                  <Text style={styles.paymentTitle}>Payment Information</Text>
                </View>
                <Text style={styles.paymentText}>
                  Payment will be processed after the venue owner confirms your booking.
                  You will receive a notification with payment instructions.
                </Text>
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Review Modal */}
      <Modal
        visible={showReviewModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowReviewModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Write Review</Text>
            <TouchableOpacity onPress={handleSubmitReview} disabled={loading}>
              <Text style={[styles.modalConfirmText, loading && { opacity: 0.5 }]}>
                {loading ? 'Submitting...' : 'Submit'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.reviewForm}>
              <Text style={styles.reviewFormTitle}>Rate Your Experience</Text>

              <View style={styles.ratingSection}>
                <Text style={styles.ratingLabel}>Rating</Text>
                <View style={styles.starRating}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <TouchableOpacity
                      key={star}
                      onPress={() => setReviewRating(star)}
                      style={styles.starButton}
                    >
                      <Star
                        size={32}
                        color={star <= reviewRating ? "#F59E0B" : "#E5E7EB"}
                        fill={star <= reviewRating ? "#F59E0B" : "#E5E7EB"}
                      />
                    </TouchableOpacity>
                  ))}
                </View>
                <Text style={styles.ratingText}>
                  {reviewRating === 1 ? 'Poor' :
                   reviewRating === 2 ? 'Fair' :
                   reviewRating === 3 ? 'Good' :
                   reviewRating === 4 ? 'Very Good' : 'Excellent'}
                </Text>
              </View>

              <View style={styles.commentSection}>
                <Text style={styles.commentLabel}>Comment (Optional)</Text>
                <TextInput
                  style={styles.commentInput}
                  value={reviewComment}
                  onChangeText={setReviewComment}
                  placeholder="Share your experience with this venue..."
                  multiline
                  numberOfLines={4}
                  maxLength={500}
                />
                <Text style={styles.characterCount}>
                  {reviewComment.length}/500 characters
                </Text>
              </View>

              <View style={styles.anonymousSection}>
                <TouchableOpacity
                  style={styles.anonymousToggle}
                  onPress={() => setIsAnonymous(!isAnonymous)}
                >
                  <View style={[styles.checkbox, isAnonymous && styles.checkboxActive]}>
                    {isAnonymous && <Text style={styles.checkmark}>✓</Text>}
                  </View>
                  <Text style={styles.anonymousLabel}>Post anonymously</Text>
                </TouchableOpacity>
                <Text style={styles.anonymousDescription}>
                  Your name will not be shown with this review
                </Text>
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Contact Modal */}
      <Modal
        visible={showContactModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowContactModal(false)}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Contact Venue Owner</Text>
            <View style={{ width: 24 }} />
          </View>

          <View style={styles.contactModalContent}>
            <View style={styles.contactCard}>
              <View style={styles.contactHeader}>
                <View style={styles.contactAvatar}>
                  <Users size={32} color="#22C55E" />
                </View>
                <View style={styles.contactInfo}>
                  <Text style={styles.contactName}>{venue.owner_name || 'Venue Owner'}</Text>
                  <Text style={styles.contactRole}>Venue Owner</Text>
                </View>
              </View>

              <View style={styles.contactDetails}>
                <View style={styles.contactRow}>
                  <Phone size={20} color="#6B7280" />
                  <View style={styles.contactText}>
                    <Text style={styles.contactLabel}>Phone Number</Text>
                    <Text style={styles.contactValue}>{venue.phone || 'Not available'}</Text>
                  </View>
                  {venue.phone && (
                    <TouchableOpacity
                      style={styles.copyButton}
                      onPress={() => copyPhoneNumber(venue.phone)}
                    >
                      <Copy size={16} color="#3B82F6" />
                    </TouchableOpacity>
                  )}
                </View>

                <View style={styles.contactRow}>
                  <MapPin size={20} color="#6B7280" />
                  <View style={styles.contactText}>
                    <Text style={styles.contactLabel}>Venue Address</Text>
                    <Text style={styles.contactValue}>{venue.address}, {venue.city}</Text>
                  </View>
                </View>
              </View>

              <View style={styles.contactActions}>
                <Text style={styles.contactNote}>
                  Tap the copy icon to copy the phone number to your clipboard, then paste it in your phone app to call.
                </Text>
              </View>
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
  },
  venueImage: {
    width: '100%',
    height: 250,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#3B82F6',
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  venueInfo: {
    padding: 24,
  },
  venueHeader: {
    marginBottom: 12,
  },
  venueName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    marginLeft: 4,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  ratingCount: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationText: {
    marginLeft: 6,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    flex: 1,
  },
  venueStats: {
    flexDirection: 'row',
    gap: 24,
    marginBottom: 24,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 6,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  amenityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  hoursContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  hourTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  hourText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  bookingSection: {
    backgroundColor: '#F9FAFB',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  dateSelection: {
    marginBottom: 20,
  },
  durationSelection: {
    marginBottom: 20,
  },
  durationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  durationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  durationButtonDisabled: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
  },
  durationText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    minWidth: 80,
    textAlign: 'center',
  },
  selectionLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  dateScroll: {
    flexDirection: 'row',
  },
  dateButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dateButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  dateButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  dateButtonTextActive: {
    color: '#FFFFFF',
  },
  timeSelection: {
    marginBottom: 20,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  timeButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  timeButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  timeButtonUnavailable: {
    backgroundColor: '#F3F4F6',
    borderColor: '#E5E7EB',
  },
  timeButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  timeButtonTextActive: {
    color: '#FFFFFF',
  },
  timeButtonTextUnavailable: {
    color: '#D1D5DB',
  },
  timeButtonPrice: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 2,
  },
  timeButtonPriceActive: {
    color: '#FFFFFF',
  },
  timeButtonNote: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginTop: 1,
    textAlign: 'center',
  },
  timeButtonInRange: {
    backgroundColor: '#22C55E20',
    borderColor: '#22C55E',
  },
  timeButtonTextInRange: {
    color: '#22C55E',
  },
  timeButtonPriceInRange: {
    color: '#22C55E',
  },
  durationNote: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 8,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  durationInfo: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#22C55E',
    marginTop: 8,
    textAlign: 'center',
  },
  timeButtonRangeEnd: {
    backgroundColor: '#22C55E40',
    borderColor: '#22C55E',
    borderWidth: 2,
  },
  rangeIndicator: {
    position: 'absolute',
    bottom: 2,
    left: 2,
    right: 2,
    backgroundColor: '#22C55E',
    borderRadius: 2,
    paddingVertical: 1,
  },
  rangeIndicatorText: {
    fontSize: 8,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  pricingInfo: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  pricingLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  pricingValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 8,
    marginTop: 8,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  totalValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#22C55E',
  },
  pricingBreakdown: {
    marginVertical: 8,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#F3F4F6',
  },
  hourBreakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 2,
  },
  hourBreakdownTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  hourBreakdownPrice: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  bookButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#22C55E',
    paddingVertical: 16,
    borderRadius: 12,
  },
  bookButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  bookButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  contactSection: {
    marginBottom: 24,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
  },
  contactButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#3B82F6',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  modalCancelText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalConfirmText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#22C55E',
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  bookingSummary: {
    gap: 20,
  },
  summaryTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  summaryValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    flex: 1,
    textAlign: 'right',
  },
  totalSummaryRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
    marginTop: 8,
    marginBottom: 0,
  },
  summaryTotalLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  summaryTotalValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#22C55E',
  },
  notesSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notesLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  paymentInfo: {
    backgroundColor: '#EFF6FF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  paymentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1E40AF',
    marginLeft: 8,
  },
  paymentText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#1E40AF',
    lineHeight: 20,
  },
  // Pricing styles
  pricingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  pricingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minWidth: '45%',
    flex: 1,
  },
  pricingLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 4,
  },
  pricingValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#22C55E',
    marginBottom: 2,
  },
  pricingTime: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  // Strikes styles
  strikesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  strikesContainer: {
    gap: 12,
  },
  strikeCard: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  strikeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  strikeType: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#DC2626',
  },
  strikeSeverity: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    textTransform: 'uppercase',
  },
  strikeReason: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#7F1D1D',
    marginBottom: 4,
  },
  strikeDate: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#991B1B',
  },
  // Reviews styles
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  reviewsActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  addReviewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#22C55E',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  addReviewText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  showMoreText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  reviewsContainer: {
    gap: 12,
  },
  reviewCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  reviewerInfo: {
    flex: 1,
  },
  reviewerName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  reviewRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  reviewRatingText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 4,
  },
  reviewDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  reviewComment: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 20,
  },
  noReviewsContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  noReviewsText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 12,
    marginBottom: 4,
  },
  noReviewsSubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
  },
  // Review form styles
  reviewForm: {
    padding: 4,
  },
  reviewFormTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 24,
    textAlign: 'center',
  },
  ratingSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  ratingLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 12,
  },
  starRating: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  starButton: {
    padding: 4,
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  commentSection: {
    marginBottom: 24,
  },
  commentLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 8,
  },
  commentInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    textAlignVertical: 'top',
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'right',
    marginTop: 4,
  },
  anonymousSection: {
    marginBottom: 24,
  },
  anonymousToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Inter-Bold',
  },
  anonymousLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  anonymousDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 28,
  },
  // Contact modal styles
  contactModalContent: {
    flex: 1,
    padding: 24,
  },
  contactCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  contactAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  contactRole: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  contactDetails: {
    marginBottom: 24,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  contactText: {
    marginLeft: 12,
    flex: 1,
  },
  contactLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 2,
  },
  contactValue: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  copyButton: {
    padding: 8,
    backgroundColor: '#F0F9FF',
    borderRadius: 6,
  },
  contactActions: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  contactNote: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 16,
  },
});