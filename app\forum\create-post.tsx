import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { forumService } from '@/lib/database';
import { ArrowLeft, Calendar, MapPin, Clock } from 'lucide-react-native';
import { useRouter } from 'expo-router';

export default function CreatePostScreen() {
  const { user } = useAuth();
  const { bookings, venues } = useData();
  const router = useRouter();
  
  const [selectedBookingId, setSelectedBookingId] = useState<string>('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [lookingForPlayers, setLookingForPlayers] = useState('1');
  const [skillLevel, setSkillLevel] = useState('');
  const [contactPreference, setContactPreference] = useState<'phone' | 'app' | 'both'>('phone');
  const [loading, setLoading] = useState(false);

  // Get user's confirmed bookings that can be posted to forum
  const eligibleBookings = bookings.filter(booking => 
    booking.player_id === user?.id && 
    booking.status === 'confirmed' &&
    new Date(booking.booking_date) >= new Date()
  );

  const handleSubmit = async () => {
    if (!selectedBookingId) {
      Alert.alert('Error', 'Please select a booking');
      return;
    }
    
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a title');
      return;
    }

    if (!lookingForPlayers || parseInt(lookingForPlayers) < 1) {
      Alert.alert('Error', 'Please enter a valid number of players needed');
      return;
    }

    try {
      setLoading(true);
      
      await forumService.createForumPost({
        booking_id: selectedBookingId,
        player_id: user!.id,
        title: title.trim(),
        description: description.trim() || undefined,
        looking_for_players: parseInt(lookingForPlayers),
        skill_level: skillLevel || undefined,
        contact_preference,
        is_active: true,
        status: 'open'
      });

      Alert.alert(
        'Success',
        'Your forum post has been created successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      console.error('Error creating forum post:', error);
      Alert.alert('Error', 'Failed to create forum post. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const renderBookingOption = (booking: any) => {
    const venue = venues.find(v => v.id === booking.venue_id);
    
    return (
      <TouchableOpacity
        key={booking.id}
        style={[
          styles.bookingOption,
          selectedBookingId === booking.id && styles.selectedBooking
        ]}
        onPress={() => setSelectedBookingId(booking.id)}
      >
        <View style={styles.bookingHeader}>
          <Text style={styles.venueName}>{venue?.name}</Text>
          <View style={styles.radioButton}>
            {selectedBookingId === booking.id && <View style={styles.radioButtonInner} />}
          </View>
        </View>
        
        <View style={styles.bookingDetails}>
          <View style={styles.detailRow}>
            <MapPin size={14} color="#6B7280" />
            <Text style={styles.detailText}>{venue?.address}</Text>
          </View>
          <View style={styles.detailRow}>
            <Calendar size={14} color="#6B7280" />
            <Text style={styles.detailText}>{formatDate(booking.booking_date)}</Text>
          </View>
          <View style={styles.detailRow}>
            <Clock size={14} color="#6B7280" />
            <Text style={styles.detailText}>
              {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Create Forum Post</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Booking</Text>
          <Text style={styles.sectionSubtitle}>
            Choose which booking you want to find teammates for
          </Text>
          
          {eligibleBookings.length > 0 ? (
            <View style={styles.bookingsList}>
              {eligibleBookings.map(renderBookingOption)}
            </View>
          ) : (
            <View style={styles.noBookings}>
              <Text style={styles.noBookingsText}>
                You don't have any confirmed bookings available for forum posts.
              </Text>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Post Details</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Title *</Text>
            <TextInput
              style={styles.textInput}
              value={title}
              onChangeText={setTitle}
              placeholder="e.g., Looking for 2 players for evening match"
              placeholderTextColor="#9CA3AF"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Add any additional details about your game..."
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Players Needed *</Text>
            <TextInput
              style={styles.textInput}
              value={lookingForPlayers}
              onChangeText={setLookingForPlayers}
              placeholder="1"
              placeholderTextColor="#9CA3AF"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Skill Level</Text>
            <View style={styles.skillLevelOptions}>
              {['Beginner', 'Intermediate', 'Advanced'].map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.skillOption,
                    skillLevel === level && styles.selectedSkillOption
                  ]}
                  onPress={() => setSkillLevel(skillLevel === level ? '' : level)}
                >
                  <Text style={[
                    styles.skillOptionText,
                    skillLevel === level && styles.selectedSkillOptionText
                  ]}>
                    {level}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Contact Preference</Text>
            <View style={styles.contactOptions}>
              {[
                { key: 'phone', label: 'Phone' },
                { key: 'app', label: 'In-App' },
                { key: 'both', label: 'Both' }
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.contactOption,
                    contactPreference === option.key && styles.selectedContactOption
                  ]}
                  onPress={() => setContactPreference(option.key as any)}
                >
                  <Text style={[
                    styles.contactOptionText,
                    contactPreference === option.key && styles.selectedContactOptionText
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Creating Post...' : 'Create Post'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginBottom: 12,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 16,
  },
  bookingsList: {
    gap: 12,
  },
  bookingOption: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 16,
  },
  selectedBooking: {
    borderColor: '#22C55E',
    backgroundColor: '#22C55E05',
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  venueName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#22C55E',
  },
  bookingDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 8,
  },
  noBookings: {
    padding: 20,
    alignItems: 'center',
  },
  noBookingsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: 100,
  },
  skillLevelOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  skillOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
  },
  selectedSkillOption: {
    borderColor: '#22C55E',
    backgroundColor: '#22C55E',
  },
  skillOptionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  selectedSkillOptionText: {
    color: '#FFFFFF',
  },
  contactOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  contactOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
  },
  selectedContactOption: {
    borderColor: '#3B82F6',
    backgroundColor: '#3B82F6',
  },
  contactOptionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  selectedContactOptionText: {
    color: '#FFFFFF',
  },
  footer: {
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  submitButton: {
    backgroundColor: '#22C55E',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
