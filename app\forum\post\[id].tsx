import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { forumService, forumOfferService } from '@/lib/database';
import { ForumPost, ForumOffer } from '@/types';
import { 
  ArrowLeft, 
  Users, 
  Calendar, 
  MapPin, 
  Clock, 
  Phone, 
  MessageCircle,
  Star
} from 'lucide-react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';

export default function PostDetailsScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  const [post, setPost] = useState<ForumPost | null>(null);
  const [offers, setOffers] = useState<ForumOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [showOfferModal, setShowOfferModal] = useState(false);

  useEffect(() => {
    if (id) {
      loadPostDetails();
      loadOffers();
    }
  }, [id]);

  const loadPostDetails = async () => {
    try {
      const postData = await forumService.getForumPostById(id!);
      setPost(postData);
    } catch (error) {
      console.error('Error loading post details:', error);
      Alert.alert('Error', 'Failed to load post details');
    }
  };

  const loadOffers = async () => {
    try {
      const offersData = await forumOfferService.getOffersForPost(id!);
      setOffers(offersData || []);
    } catch (error) {
      console.error('Error loading offers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMakeOffer = () => {
    if (!user || !post) return;
    
    if (post.player_id === user.id) {
      Alert.alert('Error', 'You cannot make an offer on your own post');
      return;
    }

    // Check if user already made an offer
    const existingOffer = offers.find(offer => offer.offering_player_id === user.id);
    if (existingOffer) {
      Alert.alert('Info', 'You have already made an offer for this post');
      return;
    }

    setShowOfferModal(true);
  };

  const handleCallPlayer = () => {
    if (!post?.player?.phone) {
      Alert.alert('Error', 'Phone number not available');
      return;
    }

    Alert.alert(
      'Call Player',
      `Do you want to call ${post.player.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Call', 
          onPress: () => Linking.openURL(`tel:${post.player.phone}`)
        }
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  if (loading || !post) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.title}>Post Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const venue = post.booking?.venue;
  const isOwnPost = post.player_id === user?.id;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Post Details</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Post Header */}
        <View style={styles.postHeader}>
          <View style={styles.playerInfo}>
            <View style={styles.playerAvatar}>
              <Text style={styles.playerInitial}>
                {post.player?.name?.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.playerDetails}>
              <Text style={styles.playerName}>{post.player?.name}</Text>
              <Text style={styles.playerLocation}>{post.player?.city}</Text>
            </View>
          </View>
          <View style={styles.playersNeeded}>
            <Users size={16} color="#22C55E" />
            <Text style={styles.playersNeededText}>
              {post.looking_for_players} player{post.looking_for_players > 1 ? 's' : ''}
            </Text>
          </View>
        </View>

        {/* Post Content */}
        <View style={styles.postContent}>
          <Text style={styles.postTitle}>{post.title}</Text>
          {post.description && (
            <Text style={styles.postDescription}>{post.description}</Text>
          )}

          {post.skill_level && (
            <View style={styles.skillLevel}>
              <Star size={14} color="#3B82F6" />
              <Text style={styles.skillLevelText}>Skill Level: {post.skill_level}</Text>
            </View>
          )}
        </View>

        {/* Booking Details */}
        <View style={styles.bookingDetails}>
          <Text style={styles.sectionTitle}>Booking Details</Text>
          
          <View style={styles.venueInfo}>
            <MapPin size={18} color="#6B7280" />
            <View style={styles.venueDetails}>
              <Text style={styles.venueName}>{venue?.name}</Text>
              <Text style={styles.venueAddress}>{venue?.address}</Text>
            </View>
          </View>

          <View style={styles.dateTimeInfo}>
            <View style={styles.dateInfo}>
              <Calendar size={16} color="#6B7280" />
              <Text style={styles.dateText}>
                {formatDate(post.booking?.booking_date || '')}
              </Text>
            </View>
            <View style={styles.timeInfo}>
              <Clock size={16} color="#6B7280" />
              <Text style={styles.timeText}>
                {formatTime(post.booking?.start_time || '')} - {formatTime(post.booking?.end_time || '')}
              </Text>
            </View>
          </View>
        </View>

        {/* Offers Section */}
        <View style={styles.offersSection}>
          <Text style={styles.sectionTitle}>
            Offers ({offers.length})
          </Text>
          
          {offers.length > 0 ? (
            <View style={styles.offersList}>
              {offers.map((offer) => (
                <View key={offer.id} style={styles.offerCard}>
                  <View style={styles.offerHeader}>
                    <View style={styles.offerPlayerInfo}>
                      <View style={styles.offerPlayerAvatar}>
                        <Text style={styles.offerPlayerInitial}>
                          {offer.offering_player?.name?.charAt(0).toUpperCase()}
                        </Text>
                      </View>
                      <View>
                        <Text style={styles.offerPlayerName}>
                          {offer.offering_player?.name}
                        </Text>
                        <Text style={styles.offerPlayerLocation}>
                          {offer.offering_player?.city}
                        </Text>
                      </View>
                    </View>
                    <View style={[
                      styles.offerStatus,
                      { backgroundColor: getOfferStatusColor(offer.status) + '20' }
                    ]}>
                      <Text style={[
                        styles.offerStatusText,
                        { color: getOfferStatusColor(offer.status) }
                      ]}>
                        {offer.status.charAt(0).toUpperCase() + offer.status.slice(1)}
                      </Text>
                    </View>
                  </View>
                  
                  {offer.message && (
                    <Text style={styles.offerMessage}>{offer.message}</Text>
                  )}
                  
                  <View style={styles.offerDetails}>
                    <Text style={styles.offerDetailText}>
                      Players: {offer.players_count}
                    </Text>
                    {offer.skill_level && (
                      <Text style={styles.offerDetailText}>
                        Skill: {offer.skill_level}
                      </Text>
                    )}
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.noOffers}>
              <MessageCircle size={32} color="#D1D5DB" />
              <Text style={styles.noOffersText}>No offers yet</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Action Buttons */}
      {!isOwnPost && post.status === 'open' && (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.callButton}
            onPress={handleCallPlayer}
          >
            <Phone size={20} color="#3B82F6" />
            <Text style={styles.callButtonText}>Call</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.offerButton}
            onPress={handleMakeOffer}
          >
            <Text style={styles.offerButtonText}>Make Offer</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

function getOfferStatusColor(status: string) {
  switch (status) {
    case 'accepted': return '#22C55E';
    case 'rejected': return '#EF4444';
    case 'cancelled': return '#6B7280';
    default: return '#F59E0B';
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  playerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  playerAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#22C55E',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  playerInitial: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  playerDetails: {
    flex: 1,
  },
  playerName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  playerLocation: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  playersNeeded: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#22C55E20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  playersNeededText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#22C55E',
    marginLeft: 6,
  },
  postContent: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  postTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  postDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 24,
    marginBottom: 16,
  },
  skillLevel: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3B82F620',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  skillLevelText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
    marginLeft: 6,
  },
  bookingDetails: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginTop: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  venueInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  venueDetails: {
    marginLeft: 12,
    flex: 1,
  },
  venueName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  venueAddress: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  dateTimeInfo: {
    gap: 12,
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    marginLeft: 8,
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    marginLeft: 8,
  },
  offersSection: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginTop: 12,
  },
  offersList: {
    gap: 12,
  },
  offerCard: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 16,
  },
  offerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  offerPlayerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  offerPlayerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  offerPlayerInitial: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  offerPlayerName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  offerPlayerLocation: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  offerStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  offerStatusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  offerMessage: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    marginBottom: 8,
  },
  offerDetails: {
    flexDirection: 'row',
    gap: 16,
  },
  offerDetailText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  noOffers: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  noOffersText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  callButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F620',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
  },
  callButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#3B82F6',
    marginLeft: 8,
  },
  offerButton: {
    backgroundColor: '#22C55E',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 2,
    alignItems: 'center',
  },
  offerButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
