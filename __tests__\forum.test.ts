/**
 * Forum Feature Test Suite
 * 
 * This test suite covers the core functionality of the forum feature including:
 * - Forum post creation and management
 * - Forum offer creation and management
 * - Data validation and error handling
 * - Edge cases and boundary conditions
 */

import { forumService, forumOfferService } from '@/lib/database';
import { ForumPost, ForumOffer } from '@/types';

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
          order: jest.fn(() => ({
            data: [],
            error: null
          }))
        })),
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => ({
              data: mockForumPost,
              error: null
            }))
          }))
        })),
        update: jest.fn(() => ({
          eq: jest.fn(() => ({
            select: jest.fn(() => ({
              single: jest.fn(() => ({
                data: mockForumPost,
                error: null
              }))
            }))
          }))
        })),
        delete: jest.fn(() => ({
          eq: jest.fn(() => ({
            error: null
          }))
        }))
      }))
    })),
    auth: {
      getUser: jest.fn(() => ({
        data: { user: { id: 'test-user-id' } },
        error: null
      }))
    }
  }
}));

const mockForumPost: ForumPost = {
  id: 'test-post-id',
  booking_id: 'test-booking-id',
  player_id: 'test-player-id',
  title: 'Test Forum Post',
  description: 'Test description',
  looking_for_players: 2,
  skill_level: 'Intermediate',
  contact_preference: 'phone',
  is_active: true,
  status: 'open',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

const mockForumOffer: ForumOffer = {
  id: 'test-offer-id',
  forum_post_id: 'test-post-id',
  offering_player_id: 'test-offering-player-id',
  message: 'Test offer message',
  players_count: 1,
  skill_level: 'Intermediate',
  contact_info: '+1234567890',
  status: 'pending',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

describe('Forum Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Forum Posts', () => {
    test('should create a forum post successfully', async () => {
      const postData = {
        booking_id: 'test-booking-id',
        player_id: 'test-player-id',
        title: 'Test Post',
        description: 'Test description',
        looking_for_players: 2,
        skill_level: 'Intermediate',
        contact_preference: 'phone' as const,
        is_active: true,
        status: 'open' as const
      };

      const result = await forumService.createForumPost(postData);
      expect(result).toBeDefined();
      expect(result.title).toBe(postData.title);
    });

    test('should validate required fields when creating a post', async () => {
      const invalidPostData = {
        booking_id: '',
        player_id: 'test-player-id',
        title: '',
        looking_for_players: 0,
        contact_preference: 'phone' as const,
        is_active: true,
        status: 'open' as const
      };

      await expect(forumService.createForumPost(invalidPostData))
        .rejects.toThrow();
    });

    test('should update forum post status', async () => {
      const updates = { status: 'closed' as const, is_active: false };
      const result = await forumService.updateForumPost('test-post-id', updates);
      expect(result).toBeDefined();
    });

    test('should close forum post', async () => {
      const result = await forumService.closeForumPost('test-post-id');
      expect(result).toBeDefined();
    });
  });

  describe('Forum Offers', () => {
    test('should create a forum offer successfully', async () => {
      const offerData = {
        forum_post_id: 'test-post-id',
        offering_player_id: 'test-offering-player-id',
        message: 'Test offer',
        players_count: 1,
        skill_level: 'Intermediate',
        contact_info: '+1234567890',
        status: 'pending' as const
      };

      const result = await forumOfferService.createOffer(offerData);
      expect(result).toBeDefined();
      expect(result.message).toBe(offerData.message);
    });

    test('should validate players count', async () => {
      const invalidOfferData = {
        forum_post_id: 'test-post-id',
        offering_player_id: 'test-offering-player-id',
        players_count: 0,
        status: 'pending' as const
      };

      await expect(forumOfferService.createOffer(invalidOfferData))
        .rejects.toThrow();
    });

    test('should update offer status', async () => {
      const result = await forumOfferService.updateOfferStatus(
        'test-offer-id',
        'accepted',
        'Offer accepted!'
      );
      expect(result).toBeDefined();
    });

    test('should reject other offers when one is accepted', async () => {
      const result = await forumOfferService.rejectOtherOffers(
        'test-post-id',
        'accepted-offer-id'
      );
      expect(result).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    test('should handle empty results gracefully', async () => {
      const result = await forumService.getForumPosts();
      expect(Array.isArray(result)).toBe(true);
    });

    test('should handle network errors', async () => {
      // Mock network error
      const mockError = new Error('Network error');
      jest.spyOn(forumService, 'getForumPosts').mockRejectedValue(mockError);

      await expect(forumService.getForumPosts()).rejects.toThrow('Network error');
    });

    test('should validate user permissions', async () => {
      // Test that users can only create posts for their own bookings
      const unauthorizedPostData = {
        booking_id: 'other-user-booking',
        player_id: 'other-user-id',
        title: 'Unauthorized Post',
        looking_for_players: 1,
        contact_preference: 'phone' as const,
        is_active: true,
        status: 'open' as const
      };

      await expect(forumService.createForumPost(unauthorizedPostData))
        .rejects.toThrow();
    });
  });
});

describe('Data Validation', () => {
  test('should validate forum post data structure', () => {
    const validPost = {
      id: 'test-id',
      booking_id: 'booking-id',
      player_id: 'player-id',
      title: 'Valid Title',
      looking_for_players: 2,
      contact_preference: 'phone' as const,
      is_active: true,
      status: 'open' as const,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    };

    expect(validPost.title).toBeTruthy();
    expect(validPost.looking_for_players).toBeGreaterThan(0);
    expect(['phone', 'app', 'both']).toContain(validPost.contact_preference);
    expect(['open', 'closed', 'cancelled']).toContain(validPost.status);
  });

  test('should validate forum offer data structure', () => {
    const validOffer = {
      id: 'test-id',
      forum_post_id: 'post-id',
      offering_player_id: 'player-id',
      players_count: 1,
      status: 'pending' as const,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    };

    expect(validOffer.forum_post_id).toBeTruthy();
    expect(validOffer.offering_player_id).toBeTruthy();
    expect(validOffer.players_count).toBeGreaterThan(0);
    expect(['pending', 'accepted', 'rejected', 'cancelled']).toContain(validOffer.status);
  });
});

describe('Business Logic', () => {
  test('should prevent players from offering on their own posts', () => {
    const postOwnerId = 'player-123';
    const offeringPlayerId = 'player-123'; // Same player

    expect(postOwnerId).toBe(offeringPlayerId);
    // This should be prevented in the business logic
  });

  test('should prevent duplicate offers from same player', () => {
    const existingOffers = [
      { offering_player_id: 'player-123', forum_post_id: 'post-456' }
    ];
    
    const newOfferPlayerId = 'player-123';
    const postId = 'post-456';

    const hasExistingOffer = existingOffers.some(
      offer => offer.offering_player_id === newOfferPlayerId && 
               offer.forum_post_id === postId
    );

    expect(hasExistingOffer).toBe(true);
    // This should be prevented in the business logic
  });

  test('should validate players count does not exceed requirement', () => {
    const postRequirement = 2;
    const offerPlayersCount = 3;

    expect(offerPlayersCount).toBeGreaterThan(postRequirement);
    // This should be prevented in the business logic
  });
});
