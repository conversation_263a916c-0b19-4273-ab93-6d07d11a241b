import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Modal,
  Pressable,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useData } from '@/contexts/DataContext';
import { useAuth } from '@/contexts/AuthContext';
import { Search, Star, MapPin, DollarSign, Filter, X, Calendar, Users, Clock } from 'lucide-react-native';

interface FilterState {
  city: string;
  priceRange: [number, number];
  rating: number;
  amenities: string[];
  availableDate: string;
}

export default function PlayerDiscover() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    city: '',
    priceRange: [0, 10000],
    rating: 0,
    amenities: [],
    availableDate: '',
  });

  const { venues } = useData();
  const { user } = useAuth();
  const router = useRouter();

  const approvedVenues = venues.filter(venue => venue.status === 'approved');

  // Apply all filters
  const filteredVenues = approvedVenues.filter(venue => {
    // Search query filter
    const matchesSearch = venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         venue.city.toLowerCase().includes(searchQuery.toLowerCase());

    // City filter
    const matchesCity = !filters.city || venue.city.toLowerCase().includes(filters.city.toLowerCase());

    // Price range filter
    const matchesPrice = venue.day_price_per_hour >= filters.priceRange[0] &&
                        venue.day_price_per_hour <= filters.priceRange[1];

    // Rating filter
    const matchesRating = venue.rating >= filters.rating;

    // Amenities filter
    const matchesAmenities = filters.amenities.length === 0 ||
                            filters.amenities.every(amenity =>
                              (venue.amenities || []).some(venueAmenity =>
                                venueAmenity.toLowerCase().includes(amenity.toLowerCase())
                              )
                            );

    return matchesSearch && matchesCity && matchesPrice && matchesRating && matchesAmenities;
  });

  // Get unique cities for filter options
  const availableCities = [...new Set(approvedVenues.map(venue => venue.city))];

  // Get unique amenities for filter options
  const availableAmenities = [...new Set(
    approvedVenues.flatMap(venue => venue.amenities || [])
  )];

  const clearFilters = () => {
    setFilters({
      city: '',
      priceRange: [0, 10000],
      rating: 0,
      amenities: [],
      availableDate: '',
    });
  };

  const hasActiveFilters = filters.city || filters.priceRange[0] > 0 || filters.priceRange[1] < 10000 ||
                          filters.rating > 0 || filters.amenities.length > 0 || filters.availableDate;

  const renderVenueCard = (venue: any) => (
    <TouchableOpacity
      key={venue.id}
      style={styles.venueCard}
      onPress={() => router.push(`/venue/${venue.id}`)}
    >
      <Image
        source={{ uri: venue.images?.[0] || 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg' }}
        style={styles.venueImage}
        resizeMode="cover"
      />
      <View style={styles.venueInfo}>
        <View style={styles.venueHeader}>
          <Text style={styles.venueName} numberOfLines={1}>
            {venue.name}
          </Text>
          <View style={styles.ratingContainer}>
            <Star size={16} color="#F59E0B" fill="#F59E0B" />
            <Text style={styles.rating}>{venue.rating}</Text>
          </View>
        </View>
        <View style={styles.venueLocation}>
          <MapPin size={14} color="#6B7280" />
          <Text style={styles.locationText} numberOfLines={1}>
            {venue.address}, {venue.city}
          </Text>
        </View>
        <View style={styles.venuePrice}>
          <DollarSign size={16} color="#22C55E" />
          <Text style={styles.priceText}>PKR {venue.day_price_per_hour}/hour</Text>
        </View>
        <View style={styles.venueFacilities}>
          {(venue.amenities || []).slice(0, 3).map((amenity: string, index: number) => (
            <View key={index} style={styles.facilityTag}>
              <Text style={styles.facilityText}>{amenity}</Text>
            </View>
          ))}
          {(venue.amenities || []).length > 3 && (
            <Text style={styles.moreFacilities}>+{(venue.amenities || []).length - 3} more</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Welcome back, {user?.name?.split(' ')[0] || 'User'}!</Text>
        <Text style={styles.subtitle}>Find your perfect football venue</Text>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color="#6B7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search venues or locations..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <TouchableOpacity
          style={[styles.filterButton, hasActiveFilters && styles.filterButtonActive]}
          onPress={() => setShowFilterModal(true)}
        >
          <Filter size={20} color={hasActiveFilters ? "#FFFFFF" : "#6B7280"} />
          {hasActiveFilters && <View style={styles.filterIndicator} />}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popular Venues</Text>
          <Text style={styles.sectionSubtitle}>
            {filteredVenues.length} venue{filteredVenues.length !== 1 ? 's' : ''} available
          </Text>
        </View>

        <View style={styles.venueList}>
          {filteredVenues.map(renderVenueCard)}
        </View>

        {filteredVenues.length === 0 && (
          <View style={styles.emptyState}>
            <Search size={48} color="#D1D5DB" />
            <Text style={styles.emptyStateTitle}>No venues found</Text>
            <Text style={styles.emptyStateText}>
              Try adjusting your search or check back later for new venues
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowFilterModal(false)}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Filter Venues</Text>
            <TouchableOpacity onPress={clearFilters}>
              <Text style={styles.clearText}>Clear</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* City Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>City</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.cityScroll}>
                <TouchableOpacity
                  style={[styles.cityButton, !filters.city && styles.cityButtonActive]}
                  onPress={() => setFilters(prev => ({ ...prev, city: '' }))}
                >
                  <Text style={[styles.cityButtonText, !filters.city && styles.cityButtonTextActive]}>
                    All Cities
                  </Text>
                </TouchableOpacity>
                {availableCities.map(city => (
                  <TouchableOpacity
                    key={city}
                    style={[styles.cityButton, filters.city === city && styles.cityButtonActive]}
                    onPress={() => setFilters(prev => ({ ...prev, city: city === filters.city ? '' : city }))}
                  >
                    <Text style={[styles.cityButtonText, filters.city === city && styles.cityButtonTextActive]}>
                      {city}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Price Range Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Price Range (PKR per hour)</Text>
              <View style={styles.priceRangeContainer}>
                <View style={styles.priceInputContainer}>
                  <Text style={styles.priceLabel}>Min</Text>
                  <TextInput
                    style={styles.priceInput}
                    value={filters.priceRange[0].toString()}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 0;
                      setFilters(prev => ({ ...prev, priceRange: [value, prev.priceRange[1]] }));
                    }}
                    keyboardType="numeric"
                    placeholder="0"
                  />
                </View>
                <Text style={styles.priceSeparator}>to</Text>
                <View style={styles.priceInputContainer}>
                  <Text style={styles.priceLabel}>Max</Text>
                  <TextInput
                    style={styles.priceInput}
                    value={filters.priceRange[1].toString()}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 10000;
                      setFilters(prev => ({ ...prev, priceRange: [prev.priceRange[0], value] }));
                    }}
                    keyboardType="numeric"
                    placeholder="10000"
                  />
                </View>
              </View>
            </View>

            {/* Rating Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Minimum Rating</Text>
              <View style={styles.ratingContainer}>
                {[0, 1, 2, 3, 4, 5].map(rating => (
                  <TouchableOpacity
                    key={rating}
                    style={[styles.ratingButton, filters.rating === rating && styles.ratingButtonActive]}
                    onPress={() => setFilters(prev => ({ ...prev, rating }))}
                  >
                    <Star
                      size={20}
                      color={filters.rating === rating ? "#FFFFFF" : "#F59E0B"}
                      fill={rating > 0 ? "#F59E0B" : "none"}
                    />
                    <Text style={[styles.ratingButtonText, filters.rating === rating && styles.ratingButtonTextActive]}>
                      {rating === 0 ? 'Any' : `${rating}+`}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Amenities Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Amenities</Text>
              <View style={styles.amenitiesContainer}>
                {availableAmenities.map(amenity => (
                  <TouchableOpacity
                    key={amenity}
                    style={[
                      styles.amenityButton,
                      filters.amenities.includes(amenity) && styles.amenityButtonActive
                    ]}
                    onPress={() => {
                      setFilters(prev => ({
                        ...prev,
                        amenities: prev.amenities.includes(amenity)
                          ? prev.amenities.filter(a => a !== amenity)
                          : [...prev.amenities, amenity]
                      }));
                    }}
                  >
                    <Text style={[
                      styles.amenityButtonText,
                      filters.amenities.includes(amenity) && styles.amenityButtonTextActive
                    ]}>
                      {amenity}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.applyButton}
              onPress={() => setShowFilterModal(false)}
            >
              <Text style={styles.applyButtonText}>
                Apply Filters ({filteredVenues.length} venues)
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginBottom: 24,
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  filterButton: {
    width: 48,
    height: 48,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    position: 'relative',
  },
  filterButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  filterIndicator: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#EF4444',
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  venueList: {
    paddingHorizontal: 24,
    gap: 16,
  },
  venueCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  venueImage: {
    width: '100%',
    height: 200,
  },
  venueInfo: {
    padding: 16,
  },
  venueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  venueName: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginRight: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  rating: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#92400E',
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    marginLeft: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    flex: 1,
  },
  venuePrice: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  priceText: {
    marginLeft: 4,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#22C55E',
  },
  venueFacilities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: 8,
  },
  facilityTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  facilityText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  moreFacilities: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 48,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  clearText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  filterSection: {
    marginVertical: 20,
  },
  filterTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  cityScroll: {
    flexDirection: 'row',
  },
  cityButton: {
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  cityButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  cityButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  cityButtonTextActive: {
    color: '#FFFFFF',
  },
  priceRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  priceInputContainer: {
    flex: 1,
  },
  priceLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 4,
  },
  priceInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  priceSeparator: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 16,
  },
  ratingContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ratingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    gap: 4,
  },
  ratingButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  ratingButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  ratingButtonTextActive: {
    color: '#FFFFFF',
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityButton: {
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  amenityButtonActive: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  amenityButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  amenityButtonTextActive: {
    color: '#FFFFFF',
  },
  modalFooter: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  applyButton: {
    backgroundColor: '#22C55E',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});