import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Modal,
} from 'react-native';
import { useData } from '@/contexts/DataContext';
import { MapPin, DollarSign, Users, Clock, CircleCheck as CheckCircle, Circle as XCircle, Eye, Star, Phone, Calendar, Shield, AlertTriangle } from 'lucide-react-native';

export default function AdminVenues() {
  const { venues, approveVenue, rejectVenue, strikes, bookings } = useData();
  const [selectedVenue, setSelectedVenue] = useState<any>(null);
  const [venueDetailsVisible, setVenueDetailsVisible] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle size={16} color="#22C55E" />;
      case 'rejected':
        return <XCircle size={16} color="#EF4444" />;
      default:
        return <Clock size={16} color="#F59E0B" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#22C55E';
      case 'rejected':
        return '#EF4444';
      default:
        return '#F59E0B';
    }
  };

  const handleApprove = (venueId: string, venueName: string) => {
    Alert.alert(
      'Approve Venue',
      `Are you sure you want to approve "${venueName}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Approve', 
          style: 'default',
          onPress: () => {
            approveVenue(venueId);
            Alert.alert('Success', 'Venue has been approved and is now live!');
          }
        },
      ]
    );
  };

  const handleReject = (venueId: string, venueName: string) => {
    Alert.alert(
      'Reject Venue',
      `Are you sure you want to reject "${venueName}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: () => {
            rejectVenue(venueId);
            Alert.alert('Venue Rejected', 'The venue owner will be notified.');
          }
        },
      ]
    );
  };

  const handleViewDetails = (venue: any) => {
    setSelectedVenue(venue);
    setVenueDetailsVisible(true);
  };

  const getVenueStrikes = (venueId: string) => {
    return strikes.filter(strike =>
      strike.venue_id === venueId && strike.status === 'active'
    );
  };

  const getVenueBookings = (venueId: string) => {
    return bookings.filter(booking => booking.venue_id === venueId);
  };

  const renderVenueCard = (venue: any) => (
    <View key={venue.id} style={styles.venueCard}>
      <Image
        source={{ uri: venue.images?.[0] || 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg' }}
        style={styles.venueImage}
        resizeMode="cover"
      />
      
      <View style={styles.venueInfo}>
        <View style={styles.venueHeader}>
          <Text style={styles.venueName} numberOfLines={1}>
            {venue.name}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(venue.status)}20` }]}>
            {getStatusIcon(venue.status)}
            <Text style={[styles.statusText, { color: getStatusColor(venue.status) }]}>
              {venue.status.charAt(0).toUpperCase() + venue.status.slice(1)}
            </Text>
          </View>
        </View>

        <View style={styles.venueDetails}>
          <View style={styles.detailRow}>
            <MapPin size={14} color="#6B7280" />
            <Text style={styles.detailText} numberOfLines={1}>
              {venue.address}, {venue.city}
            </Text>
          </View>

          <View style={styles.venueStats}>
            <View style={styles.statItem}>
              <DollarSign size={14} color="#22C55E" />
              <Text style={styles.statText}>${venue.price_per_hour}/hr</Text>
            </View>
            <View style={styles.statItem}>
              <Users size={14} color="#3B82F6" />
              <Text style={styles.statText}>{venue.capacity} people</Text>
            </View>
          </View>

          <Text style={styles.venueDescription} numberOfLines={2}>
            {venue.description}
          </Text>

          <View style={styles.amenitiesContainer}>
            {venue.amenities.slice(0, 3).map((amenity: string, index: number) => (
              <View key={index} style={styles.amenityTag}>
                <Text style={styles.amenityText}>{amenity}</Text>
              </View>
            ))}
            {venue.amenities.length > 3 && (
              <Text style={styles.moreAmenities}>+{venue.amenities.length - 3} more</Text>
            )}
          </View>
        </View>

        {venue.status === 'pending' && (
          <View style={styles.venueActions}>
            <TouchableOpacity 
              style={styles.rejectButton}
              onPress={() => handleReject(venue.id, venue.name)}
            >
              <XCircle size={16} color="#EF4444" />
              <Text style={styles.rejectButtonText}>Reject</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.approveButton}
              onPress={() => handleApprove(venue.id, venue.name)}
            >
              <CheckCircle size={16} color="#FFFFFF" />
              <Text style={styles.approveButtonText}>Approve</Text>
            </TouchableOpacity>
          </View>
        )}

        {venue.status !== 'pending' && (
          <View style={styles.venueActions}>
            <TouchableOpacity
              style={styles.viewButton}
              onPress={() => handleViewDetails(venue)}
            >
              <Eye size={16} color="#3B82F6" />
              <Text style={styles.viewButtonText}>View Details</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );

  const pendingVenues = venues.filter(venue => venue.status === 'pending');
  const approvedVenues = venues.filter(venue => venue.status === 'approved');
  const rejectedVenues = venues.filter(venue => venue.status === 'rejected');

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Venue Management</Text>
        <Text style={styles.subtitle}>
          {venues.length} total venue{venues.length !== 1 ? 's' : ''} • {pendingVenues.length} pending
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {pendingVenues.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pending Approval ({pendingVenues.length})</Text>
            <View style={styles.venueList}>
              {pendingVenues.map(renderVenueCard)}
            </View>
          </View>
        )}

        {approvedVenues.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Approved Venues ({approvedVenues.length})</Text>
            <View style={styles.venueList}>
              {approvedVenues.map(renderVenueCard)}
            </View>
          </View>
        )}

        {rejectedVenues.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Rejected Venues ({rejectedVenues.length})</Text>
            <View style={styles.venueList}>
              {rejectedVenues.map(renderVenueCard)}
            </View>
          </View>
        )}

        {venues.length === 0 && (
          <View style={styles.emptyState}>
            <MapPin size={48} color="#D1D5DB" />
            <Text style={styles.emptyStateTitle}>No venues yet</Text>
            <Text style={styles.emptyStateText}>
              Venues submitted by venue owners will appear here for review
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Venue Details Modal */}
      <Modal
        visible={venueDetailsVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setVenueDetailsVisible(false)}>
              <Text style={styles.modalCloseText}>Close</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Venue Details</Text>
            <View style={{ width: 50 }} />
          </View>

          {selectedVenue && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.venueDetailCard}>
                <Image
                  source={{ uri: selectedVenue.images?.[0] || 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg' }}
                  style={styles.venueDetailImage}
                  resizeMode="cover"
                />

                <View style={styles.venueDetailInfo}>
                  <View style={styles.venueDetailHeader}>
                    <Text style={styles.venueDetailName}>{selectedVenue.name}</Text>
                    <View style={[styles.statusBadge, { backgroundColor: `${getStatusColor(selectedVenue.status)}20` }]}>
                      {getStatusIcon(selectedVenue.status)}
                      <Text style={[styles.statusText, { color: getStatusColor(selectedVenue.status) }]}>
                        {selectedVenue.status.charAt(0).toUpperCase() + selectedVenue.status.slice(1)}
                      </Text>
                    </View>
                  </View>

                  <Text style={styles.venueDetailDescription}>{selectedVenue.description}</Text>

                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Location & Contact</Text>
                    <View style={styles.detailRow}>
                      <MapPin size={16} color="#6B7280" />
                      <Text style={styles.detailText}>{selectedVenue.address}, {selectedVenue.city}</Text>
                    </View>
                    {selectedVenue.phone && (
                      <View style={styles.detailRow}>
                        <Phone size={16} color="#6B7280" />
                        <Text style={styles.detailText}>{selectedVenue.phone}</Text>
                      </View>
                    )}
                    {selectedVenue.latitude && selectedVenue.longitude && (
                      <View style={styles.detailRow}>
                        <MapPin size={16} color="#6B7280" />
                        <Text style={styles.detailText}>
                          Coordinates: {selectedVenue.latitude}, {selectedVenue.longitude}
                        </Text>
                      </View>
                    )}
                  </View>

                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Pricing & Hours</Text>
                    <View style={styles.pricingGrid}>
                      <View style={styles.pricingCard}>
                        <Text style={styles.pricingLabel}>Day Rate</Text>
                        <Text style={styles.pricingValue}>${selectedVenue.day_price_per_hour}/hr</Text>
                      </View>
                      <View style={styles.pricingCard}>
                        <Text style={styles.pricingLabel}>Night Rate</Text>
                        <Text style={styles.pricingValue}>${selectedVenue.night_price_per_hour}/hr</Text>
                      </View>
                      {selectedVenue.weekend_day_price && (
                        <View style={styles.pricingCard}>
                          <Text style={styles.pricingLabel}>Weekend Day</Text>
                          <Text style={styles.pricingValue}>${selectedVenue.weekend_day_price}/hr</Text>
                        </View>
                      )}
                      {selectedVenue.weekend_night_price && (
                        <View style={styles.pricingCard}>
                          <Text style={styles.pricingLabel}>Weekend Night</Text>
                          <Text style={styles.pricingValue}>${selectedVenue.weekend_night_price}/hr</Text>
                        </View>
                      )}
                    </View>
                    <View style={styles.detailRow}>
                      <Clock size={16} color="#6B7280" />
                      <Text style={styles.detailText}>
                        Open: {selectedVenue.opening_time} - {selectedVenue.closing_time}
                      </Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Clock size={16} color="#6B7280" />
                      <Text style={styles.detailText}>
                        Night time starts: {selectedVenue.night_time_start}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Facilities & Amenities</Text>
                    <View style={styles.amenitiesGrid}>
                      {selectedVenue.facilities?.map((facility: string, index: number) => (
                        <View key={index} style={styles.facilityTag}>
                          <Text style={styles.facilityText}>{facility}</Text>
                        </View>
                      ))}
                    </View>
                  </View>

                  {selectedVenue.rules && (
                    <View style={styles.detailSection}>
                      <Text style={styles.detailSectionTitle}>Rules & Policies</Text>
                      <Text style={styles.rulesText}>{selectedVenue.rules}</Text>
                    </View>
                  )}

                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Statistics</Text>
                    <View style={styles.statsGrid}>
                      <View style={styles.statCard}>
                        <Text style={styles.statValue}>{selectedVenue.rating.toFixed(1)}</Text>
                        <Text style={styles.statLabel}>Rating</Text>
                      </View>
                      <View style={styles.statCard}>
                        <Text style={styles.statValue}>{selectedVenue.total_reviews}</Text>
                        <Text style={styles.statLabel}>Reviews</Text>
                      </View>
                      <View style={styles.statCard}>
                        <Text style={styles.statValue}>{getVenueBookings(selectedVenue.id).length}</Text>
                        <Text style={styles.statLabel}>Bookings</Text>
                      </View>
                      <View style={styles.statCard}>
                        <Text style={[styles.statValue, { color: getVenueStrikes(selectedVenue.id).length > 0 ? '#EF4444' : '#22C55E' }]}>
                          {getVenueStrikes(selectedVenue.id).length}
                        </Text>
                        <Text style={styles.statLabel}>Strikes</Text>
                      </View>
                    </View>
                  </View>

                  {getVenueStrikes(selectedVenue.id).length > 0 && (
                    <View style={styles.detailSection}>
                      <Text style={styles.detailSectionTitle}>Active Strikes</Text>
                      {getVenueStrikes(selectedVenue.id).map((strike, index) => (
                        <View key={index} style={styles.strikeCard}>
                          <View style={styles.strikeHeader}>
                            <AlertTriangle size={16} color="#EF4444" />
                            <Text style={styles.strikeTitle}>{strike.reason}</Text>
                          </View>
                          {strike.description && (
                            <Text style={styles.strikeDescription}>{strike.description}</Text>
                          )}
                          <Text style={styles.strikeDate}>
                            Issued on {new Date(strike.created_at).toLocaleDateString()}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}

                  <View style={styles.detailSection}>
                    <Text style={styles.detailSectionTitle}>Account Information</Text>
                    <View style={styles.detailRow}>
                      <Calendar size={16} color="#6B7280" />
                      <Text style={styles.detailText}>
                        Created: {new Date(selectedVenue.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Calendar size={16} color="#6B7280" />
                      <Text style={styles.detailText}>
                        Last updated: {new Date(selectedVenue.updated_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </ScrollView>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
    paddingHorizontal: 24,
  },
  venueList: {
    paddingHorizontal: 24,
    gap: 16,
  },
  venueCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  venueImage: {
    width: '100%',
    height: 160,
  },
  venueInfo: {
    padding: 16,
  },
  venueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  venueName: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginRight: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  venueDetails: {
    gap: 8,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    marginLeft: 6,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    flex: 1,
  },
  venueStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  venueDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: 6,
  },
  amenityTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  amenityText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  moreAmenities: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  venueActions: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  rejectButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  rejectButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  approveButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#22C55E',
  },
  approveButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  viewButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#3B82F6',
    backgroundColor: '#EFF6FF',
  },
  viewButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 48,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  modalCloseText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalContent: {
    flex: 1,
    padding: 24,
  },
  venueDetailCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  venueDetailImage: {
    width: '100%',
    height: 200,
  },
  venueDetailInfo: {
    padding: 20,
  },
  venueDetailHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  venueDetailName: {
    flex: 1,
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginRight: 12,
  },
  venueDetailDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 24,
    marginBottom: 24,
  },
  detailSection: {
    marginBottom: 24,
  },
  detailSectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  pricingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  pricingCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    minWidth: '45%',
    alignItems: 'center',
  },
  pricingLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 4,
  },
  pricingValue: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#22C55E',
  },
  amenitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  facilityTag: {
    backgroundColor: '#EFF6FF',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  facilityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#3B82F6',
  },
  rulesText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    minWidth: '22%',
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  strikeCard: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
    marginBottom: 8,
  },
  strikeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  strikeTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
    marginLeft: 8,
  },
  strikeDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#7F1D1D',
    marginBottom: 4,
  },
  strikeDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#991B1B',
  },
});