import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useData } from '@/contexts/DataContext';
import { Star, MapPin, DollarSign, Calendar } from 'lucide-react-native';

export default function PlayerVenues() {
  const { venues } = useData();
  const router = useRouter();

  const approvedVenues = venues.filter(venue => venue.status === 'approved');

  const renderVenueCard = (venue: any) => (
    <TouchableOpacity
      key={venue.id}
      style={styles.venueCard}
      onPress={() => router.push(`/venue/${venue.id}`)}
    >
      <Image
        source={{ uri: venue.images?.[0] || 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg' }}
        style={styles.venueImage}
        resizeMode="cover"
      />
      <View style={styles.venueInfo}>
        <View style={styles.venueHeader}>
          <Text style={styles.venueName} numberOfLines={2}>
            {venue.name}
          </Text>
          <View style={styles.ratingContainer}>
            <Star size={14} color="#F59E0B" fill="#F59E0B" />
            <Text style={styles.rating}>{venue.rating}</Text>
          </View>
        </View>
        <View style={styles.venueLocation}>
          <MapPin size={12} color="#6B7280" />
          <Text style={styles.locationText} numberOfLines={1}>
            {venue.city}
          </Text>
        </View>
        <View style={styles.venuePrice}>
          <DollarSign size={14} color="#22C55E" />
          <Text style={styles.priceText}>${venue.price_per_hour}/hr</Text>
        </View>
        <TouchableOpacity style={styles.bookButton}>
          <Calendar size={16} color="#FFFFFF" />
          <Text style={styles.bookButtonText}>Book Now</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>All Venues</Text>
        <Text style={styles.subtitle}>
          {approvedVenues.length} venue{approvedVenues.length !== 1 ? 's' : ''} available
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.venueGrid}>
          {approvedVenues.map(renderVenueCard)}
        </View>

        {approvedVenues.length === 0 && (
          <View style={styles.emptyState}>
            <MapPin size={48} color="#D1D5DB" />
            <Text style={styles.emptyStateTitle}>No venues available</Text>
            <Text style={styles.emptyStateText}>
              Check back later for new venues in your area
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
  venueGrid: {
    paddingHorizontal: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  venueCard: {
    width: '47%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  venueImage: {
    width: '100%',
    height: 120,
  },
  venueInfo: {
    padding: 12,
  },
  venueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  venueName: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginRight: 8,
    lineHeight: 18,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  rating: {
    marginLeft: 2,
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: '#92400E',
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  locationText: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    flex: 1,
  },
  venuePrice: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  priceText: {
    marginLeft: 2,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#22C55E',
  },
  bookButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#22C55E',
    paddingVertical: 8,
    borderRadius: 6,
  },
  bookButtonText: {
    marginLeft: 4,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 48,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});