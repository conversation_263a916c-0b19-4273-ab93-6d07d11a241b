# Signup Testing Guide

This guide explains how to test the signup functionality and troubleshoot common issues.

## Fixed Issues

### 1. Missing Database Fields
**Problem:** The signup form was missing required fields that the database expected.
**Solution:** Added the following fields to the signup form:
- CNIC (optional)
- Address (optional) 
- City (optional)

### 2. Missing RLS Policy
**Problem:** Row Level Security was blocking user registration.
**Solution:** Added INSERT policy to allow new user registration:
```sql
CREATE POLICY "Allow user registration" ON users FOR INSERT WITH CHECK (auth.uid() = id);
```

### 3. Incomplete User Data
**Problem:** The signup function wasn't passing all required fields to the database.
**Solution:** Updated the signup function to include all User interface fields with proper defaults.

## How to Test Signup

### 1. Valid Signup Test
Try creating an account with these details:
- **Name:** John <PERSON>
- **Email:** <EMAIL>
- **Phone:** +************
- **CNIC:** 12345-1234567-1 (optional)
- **Address:** 123 Main Street (optional)
- **City:** Karachi (optional)
- **Password:** password123
- **Confirm Password:** password123
- **User Type:** Player

**Expected Result:** Success message and redirect to sign-in screen.

### 2. Validation Tests

#### Email Validation
- **Invalid:** `invalid-email` → Should show "Please enter a valid email address"
- **Valid:** `<EMAIL>` → Should pass validation

#### Password Validation
- **Too Short:** `12345` → Should show "Password must be at least 6 characters"
- **Mismatch:** Different confirm password → Should show "Passwords do not match"
- **Valid:** `password123` → Should pass validation

#### Phone Validation
- **Invalid:** `123` → Should show "Please enter a valid phone number"
- **Valid:** `+************` or `***********` → Should pass validation

#### CNIC Validation
- **Invalid:** `123` → Should show "Please enter a valid CNIC (13 digits)"
- **Valid:** `12345-1234567-1` or `*************` → Should pass validation

### 3. Duplicate Email Test
Try signing up with an existing email (e.g., `<EMAIL>`):
**Expected Result:** "An account with this email already exists."

### 4. User Type Selection
Test both user types:
- **Player:** Should create a player account with status "pending"
- **Venue Owner:** Should create a venue owner account with status "pending"

## Demo Accounts for Testing Sign In

After successful signup, you can test sign in with these demo accounts:

### Admin Account
- **Email:** <EMAIL>
- **Password:** admin123
- **Status:** Approved (can access admin features)

### Player Account
- **Email:** <EMAIL>
- **Password:** player123
- **Status:** Approved (can access player features)

### Venue Owner Account
- **Email:** <EMAIL>
- **Password:** owner123
- **Status:** Approved (can access venue owner features)

## Troubleshooting Common Issues

### 1. "Sign up failed" Error
**Possible Causes:**
- Network connectivity issues
- Database connection problems
- Invalid input data
- Missing required fields

**Solutions:**
- Check internet connection
- Verify all required fields are filled
- Check console logs for detailed error messages

### 2. "An account with this email already exists"
**Cause:** Email is already registered in the system.
**Solution:** Use a different email address or try signing in instead.

### 3. Validation Errors
**Cause:** Input data doesn't meet validation requirements.
**Solution:** Follow the validation rules shown in the error messages.

### 4. "User must be authenticated" Error
**Cause:** RLS policy issue or authentication problem.
**Solution:** Check that the RLS policies are correctly set up in the database.

## Database Verification

To verify a user was created successfully, check the database:

```sql
SELECT id, email, name, user_type, status, created_at 
FROM users 
WHERE email = '<EMAIL>';
```

The user should appear with:
- **Status:** 'pending' (for non-admin users)
- **User Type:** 'player' or 'venue_owner'
- **Created At:** Recent timestamp

## Next Steps After Signup

1. **User Status:** New users have 'pending' status and need admin approval
2. **Admin Approval:** Admin can approve users through the admin dashboard
3. **Sign In:** Once approved, users can sign in with their credentials
4. **Profile Completion:** Users may need to complete their profiles after first sign in

## Testing Checklist

- [ ] Valid signup with all fields works
- [ ] Email validation works correctly
- [ ] Password validation works correctly
- [ ] Phone number validation works correctly
- [ ] CNIC validation works correctly
- [ ] Duplicate email detection works
- [ ] User type selection works
- [ ] Success message appears
- [ ] Redirect to sign-in works
- [ ] User appears in database with correct data
- [ ] User status is set to 'pending'
- [ ] Demo accounts still work for sign in

## Error Messages Reference

| Error | Meaning | Solution |
|-------|---------|----------|
| "Please fill in all required fields" | Missing name, email, or password | Fill in all required fields |
| "Please enter a valid email address" | Invalid email format | Use proper email format |
| "Passwords do not match" | Password confirmation doesn't match | Ensure both passwords are identical |
| "Password must be at least 6 characters" | Password too short | Use at least 6 characters |
| "Please enter a valid phone number" | Invalid phone format | Use proper phone format |
| "Please enter a valid CNIC (13 digits)" | Invalid CNIC format | Use 13-digit CNIC format |
| "An account with this email already exists" | Email already registered | Use different email or sign in |
| "Sign up failed. Please try again." | General error | Check network and try again |

## Support

If you encounter issues not covered in this guide:
1. Check the console logs for detailed error messages
2. Verify database connectivity
3. Ensure all RLS policies are correctly configured
4. Check that the Supabase project is properly set up
